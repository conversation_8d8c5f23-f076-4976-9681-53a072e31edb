import * as React from 'react'
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native'
import { responsiveWidth, responsiveHeight } from 'react-native-responsive-dimensions'
import Colors from '../styles/Colors'

const CustumBtn = ({ title, onPress, style, loading, disabled, ...props }) => {
  const isDisabled = loading || disabled

  return (
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.7}
        style={[
          {
            // width: responsiveWidth(80),
            height: responsiveHeight(5),
            backgroundColor: isDisabled ? Colors.gray : Colors.primary,
            justifyContent: 'center',
            alignItems: 'center',
            borderRadius: 4,
            marginVertical: responsiveHeight(2),
          },
          style,
        ]}
        disabled={isDisabled}
        {...props}
      >
        {loading ? (
          <ActivityIndicator color={Colors.white} />
        ) : (
          <Text style={{ color: Colors.white, fontSize: responsiveHeight(1.8), fontWeight:'bold' }}>{title}</Text>
        )}
      </TouchableOpacity>
  )
}

export default CustumBtn