import { View, Text, StyleSheet, SectionList, TouchableOpacity, SafeAreaView, Pressable } from 'react-native'
import React, { useState } from 'react'
import { responsiveHeight, responsiveWidth, responsiveFontSize } from 'react-native-responsive-dimensions'
import { useNavigation } from '@react-navigation/native'
import Colors from '../../styles/Colors'
import Icon from 'react-native-vector-icons/MaterialCommunityIcons'
import LinearGradient from 'react-native-linear-gradient'
import ConfirmationModal from '../../components/ConfirmationModal'

 // Sample data
  const mockTasks = [
    {
      id: '1',
      patientName: '<PERSON>',
      time: '09:00 AM',
      date: '05/08/2025',
      address: '123 Main St, Downtown',
      status: 'pending',
      totalAmount:3000,
      paymentStatus: 'Unpaid',
      paymentMethod: 'COD',
      testDetails: [
        { id: '1', name: 'CBC TEST', SLDCode: 'SLDC126', pre_test_info: 'No special preparation required',  specimen_details: '2 mL Whole Blood (EDTA)', fasting_required: 'inactive',
          speciality: {
            id: 22,
           name: 'Pediatrician', // For children
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '2', name: 'SST(Gold)', tubeColor: '#FFC000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
        { id: '2', name: 'VITAMIN D3 TEST', SLDCode: 'VSLD020', pre_test_info: "12 hours fasting required", specimen_details: "5 mL Serum", fasting_required: "active", 
          speciality: {
            id: 101,
            name: 'Internal Medicine', // For adults
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
        { id: '3', name: 'Urine Test', SLDCode: 'USLD014', pre_test_info: 'Collect midstream urine sample', specimen_details: '10 mL Urine', fasting_required: "inactive", 
          speciality: {
            id: 22,
           name: 'Pediatrician', // For children
          },
          containerType: [
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
        
      ],
    },
    {
      id: '2',
      patientName: 'Jane Smith',
      time: '11:30 AM',
      date: 'Today',
      address: '456 Oak Ave, Uptown',
      status: 'accepted',
      totalAmount:2000,
      paymentStatus: 'Paid',
      paymentMethod: 'UPI',
      testDetails: [
        { id: '1', name: 'THYROID PROFILE TEST', SLDCode: 'TSLD046',  pre_test_info: 'Sample to be collected in the morning', specimen_details: "5 mL Serum", fasting_required: "active", 
          speciality: {
            id: 102,
            name: 'Internist',  // For adults
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
        { id: '2', name: 'VITAMIN D3 TEST', SLDCode: 'VSLD020', pre_test_info: "12 hours fasting required", specimen_details: "5 mL Serum", fasting_required: "active",   
          speciality: {
            id: 101,
            name: 'Internal Medicine', // For adults
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
      ],
    }
  ];

const Home = () => {
  const navigation = useNavigation()
  const [user] = useState({ name: 'Tushar Sahu' })
  const [tasks, setTasks] = useState(mockTasks)
  const [expandedCards, setExpandedCards] = useState({})
  const [confirmationModal, setConfirmationModal] = useState({
    visible: false,
    type: '',
    taskId: null,
    taskName: '',
    paymentStatus: '',
    paymentMethod: '',
    totalAmount: 0
  })
  const [isOnline, setIsOnline] = useState(true)

  const toggleCardExpansion = (taskId) => {
    setExpandedCards(prev => ({
      ...prev,
      [taskId]: !prev[taskId]
    }))
  }

  const toggleOnlineStatus = () => {
    setIsOnline(prev => !prev)
    // You can add additional logic here like:
    // - Update server about status change
    // - Show notification to user
    // - Update location tracking
  }

  const renderTestDetail = (test) => (
    <View key={test.id} style={styles.testDetailCard}>
      <View style={styles.testHeader}>
        <Text style={styles.testName}>{test.name}</Text>
        <Text style={styles.testCode}>{test.SLDCode}</Text>
      </View>
      
      <View style={styles.testInfo}>
        <View style={styles.infoRow}>
          <Icon name="information-outline" size={responsiveFontSize(1.6)} color={Colors.info} />
          <Text style={styles.infoText}>{test.pre_test_info}</Text>
        </View>
        
        <View style={styles.infoRow}>
          <Icon name="test-tube" size={responsiveFontSize(1.6)} color={Colors.primary} />
          <Text style={styles.infoText}>{test.specimen_details}</Text>
        </View>
        
        {test.fasting_required === 'active' && (
          <View style={styles.fastingBadge}>
            <Icon name="clock-alert-outline" size={responsiveFontSize(1.4)} color={Colors.warning} />
            <Text style={styles.fastingText}>Fasting Required</Text>
          </View>
        )}
        
        <View style={styles.specialityRow}>
          <Icon name="doctor" size={responsiveFontSize(1.6)} color={Colors.success} />
          <Text style={styles.specialityText}>{test.speciality.name}</Text>
        </View>
        
        <View style={styles.containerSection}>
          <Text style={styles.containerLabel}>Container Types:</Text>
          <View style={styles.containerList}>
            {test.containerType.map((container) => (
              <View key={container.id} style={styles.containerItem}>
                <View style={[styles.colorDot, { backgroundColor: container.tubeColor }]} />
                <Text style={styles.containerName}>{container.name}</Text>
              </View>
            ))}
          </View>
        </View>
      </View>
    </View>
  )

  const sections = [
    { title: 'Pending Tasks', data: tasks.filter(task => task.status === 'pending') },
    { title: 'Accepted Tasks', data: tasks.filter(task => task.status === 'accepted') }
  ]

   const getPriorityColor = (priority) => {
    switch (priority) {
      case 'pending': return Colors.pendingColor
      case 'accepted': return Colors.info
      case 'completed': return Colors.success
      default: return Colors.error
    }
  }

  const showConfirmation = (type, taskId, taskName, paymentStatus, paymentMethod, totalAmount) => {
    setConfirmationModal({
      visible: true,
      type,
      taskId,
      taskName,
      paymentStatus: paymentStatus || '',
      paymentMethod: paymentMethod || '',
      totalAmount: totalAmount || 0
    })
  }

  const hideConfirmation = () => {
    setConfirmationModal({
      visible: false,
      type: '',
      taskId: null,
      taskName: '',
      paymentStatus: '',
      paymentMethod: '',
      totalAmount: 0
    })
  }

  const handleAcceptTask = (taskId) => {
    const task = tasks.find(t => t.id === taskId)
    showConfirmation('accept', taskId, task?.patientName || '', task?.paymentStatus, task?.paymentMethod, task?.totalAmount)
  }

  const handleDeclineTask = (taskId) => {
    const task = tasks.find(t => t.id === taskId)
    showConfirmation('reject', taskId, task?.patientName || '', task?.paymentStatus, task?.paymentMethod, task?.totalAmount)
  }

  const confirmAction = () => {
    if (confirmationModal.type === 'accept') {
      setTasks(currentTasks =>
        currentTasks.map(task =>
          task.id === confirmationModal.taskId ? { ...task, status: 'accepted' } : task
        )
      )
    } else if (confirmationModal.type === 'reject') {
      setTasks(currentTasks =>
        currentTasks.filter(task => task.id !== confirmationModal.taskId)
      )
    } else if (confirmationModal.type === 'payment') {
      // Update payment status to Paid and then show pickup confirmation
      setTasks(currentTasks =>
        currentTasks.map(task =>
          task.id === confirmationModal.taskId ? { ...task, paymentStatus: 'Paid' } : task
        )
      )
      hideConfirmation()

      // After payment is confirmed, show pickup confirmation
      setTimeout(() => {
        const task = tasks.find(t => t.id === confirmationModal.taskId)
        showConfirmation('pickup', confirmationModal.taskId, task?.patientName, 'Paid', task?.paymentMethod, task?.totalAmount)
      }, 100)
    } else if (confirmationModal.type === 'pickup') {
      setTasks(currentTasks =>
        currentTasks.map(task =>
          task.id === confirmationModal.taskId ? { ...task, status: 'completed' } : task
        )
      )
    }
    hideConfirmation()
  }

  const handlePickupSample = (taskId) => {
    const task = tasks.find(t => t.id === taskId)

    // Check if payment is required and not yet paid
    if (task?.paymentMethod === 'COD' && task?.paymentStatus !== 'Paid') {
      showConfirmation('payment', taskId, task?.patientName, task?.paymentStatus, task?.paymentMethod, task?.totalAmount)
    } else {
      showConfirmation('pickup', taskId, task?.patientName, task?.paymentStatus, task?.paymentMethod, task?.totalAmount)
    }
  }

  const renderTaskItem = ({ item }) => {
    const isExpanded = expandedCards[item.id]
    
    return (
      <View style={styles.taskCard}>
        <Pressable onPress={() => toggleCardExpansion(item.id)} style={styles.taskHeader}>
          <View style={styles.patientInfo}>
            <View style={styles.avatarSmall}>
              <Icon name="account" size={responsiveFontSize(2.5)} color={Colors.primary} />
            </View>
            <View style={styles.patientDetails}>
              <Text style={styles.patientName}>{item.patientName}</Text>
              <View style={styles.timeContainer}>
                <Icon name="clock-outline" size={responsiveFontSize(1.4)} color={Colors.primary} />
                <Text style={styles.taskTime}>{item.time}</Text>
                <Text style={styles.taskDate}>• {item.date}</Text>
              </View>
            </View>
          </View>
          <View style={styles.headerRight}>
            <View style={[styles.statusBadge, { backgroundColor: `${getPriorityColor(item.status)}18` }]}>
              <Text style={[styles.statusText, { color: getPriorityColor(item.status) }]}>
                {item.status}
              </Text>
            </View>
          </View>
        </Pressable>
        
        <Pressable onPress={() => toggleCardExpansion(item.id)} style={styles.taskDetailRow}>
          <Icon name="map-marker-outline" size={responsiveFontSize(2)} color={Colors.textSecondary} />
          <Text style={styles.taskAddress} numberOfLines={1}>{item.address}</Text>
        </Pressable>

        {/* Payment Information */}
         <View style={styles.paymentContainer}>
          <View style={styles.amountSection}>
            {/* <Icon name="currency-inr" size={responsiveFontSize(1.8)} color={Colors.black} /> */}
            <Text style={styles.amountText}>₹{item.totalAmount.toLocaleString()}/ <Text style={{color: Colors.textSecondary, fontSize: responsiveFontSize(1.4)}}>{item.paymentMethod}</Text></Text>
            <View style={[styles.paymentStatusBadge, {backgroundColor: item.paymentStatus === 'Paid' ? `${Colors.success}20` : `${Colors.error}20`}]}>
              <Icon
                name={item.paymentStatus === 'Paid' ? "check-circle" : "clock-outline"}
                size={responsiveFontSize(1.4)}
                color={item.paymentStatus === 'Paid' ? Colors.success : Colors.error}
              />
              <Text style={[styles.paymentStatusText, {color: item.paymentStatus === 'Paid' ? Colors.success : Colors.error}]}>
                {item.paymentStatus}
              </Text>
            </View>
          </View>
          {/* <View style={[styles.paymentStatusBadge, {backgroundColor: item.paymentStatus === 'Paid' ? `${Colors.success}20` : `${Colors.error}20`}]}>
            <Icon
              name={item.paymentStatus === 'Paid' ? "check-circle" : "clock-outline"}
              size={responsiveFontSize(1.4)}
              color={item.paymentStatus === 'Paid' ? Colors.success : Colors.error}
            />
            <Text style={[styles.paymentStatusText, {color: item.paymentStatus === 'Paid' ? Colors.success : Colors.error}]}>
              {item.paymentStatus}
            </Text>
          </View> */}
        </View>

        <Pressable onPress={() => toggleCardExpansion(item.id)} style={styles.testSummary}>
          <View style={{flexDirection: 'row', alignItems: 'center', gap: responsiveWidth(2)}}>
            <Icon name="flask-outline" size={responsiveFontSize(1.8)} color={Colors.info} />
            <Text style={styles.testCount}>{item.testDetails.length} Test(s) Required</Text>
          </View>
          <View style={styles.expandButton} >
            <Icon 
              name={isExpanded ? "chevron-up" : "chevron-down"} 
              size={responsiveFontSize(2.5)} 
              color={Colors.primary} 
            />
          </View> 
        </Pressable>

        {isExpanded && (
          <View style={styles.expandedContent}>
            <Text style={styles.testDetailsTitle}>Test Details:</Text>
            {item.testDetails.map(renderTestDetail)}
          </View>
        )}
        
        {item.status === 'pending' ? (
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.acceptButton}
              onPress={() => handleAcceptTask(item.id)}
            >
              <Icon name="check" size={responsiveFontSize(2)} color={Colors.white} />
              <Text style={styles.acceptButtonText}>Accept Task</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.declineButton} onPress={() => handleDeclineTask(item.id)}>
              <Icon name="close" size={responsiveFontSize(2)} color={Colors.error} />
              <Text style={styles.declineButtonText}>Decline</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.acceptedContainer}>
            <View style={styles.acceptedLeft}>
              <Icon name="check-circle" size={responsiveFontSize(2)} color={Colors.success} />
              <Text style={styles.acceptedText}>Task Accepted</Text>
            </View>
            <View style={styles.actionButtons}>
              <TouchableOpacity style={styles.goButton}>
                <Icon name="navigation" size={responsiveFontSize(1.8)} color={Colors.white} />
                <Text style={styles.goButtonText}>Navigate</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.pickupButton}
                onPress={() => handlePickupSample(item.id)}
              >
                <Icon name="test-tube" size={responsiveFontSize(1.8)} color={Colors.white} />
                <Text style={styles.goButtonText}>Sample Picked</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    )
  }

  return (
    <View style={styles.container}>
      {/* Modern Header */}
      <View style={styles.headerContainer}>
        <LinearGradient
          colors={[Colors.primary, '#4A90E2']}
          style={styles.headerGradient}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}
        >
          <SafeAreaView>
            <View style={styles.headerContent}>
              <View style={styles.userSection}>
                <View style={styles.avatarContainer}>
                  <Icon name="account-circle" size={responsiveFontSize(4)} color={Colors.white} />
                </View>
                <View style={styles.greetingContainer}>
                  <Text style={styles.greetingText}>Hello</Text>
                  <Text style={styles.userNameText}>{user.name}</Text>
                </View>
              </View>
              
              <View style={styles.headerActions}>
                {/* Online/Offline Toggle */}
                <TouchableOpacity
                  style={[styles.statusToggle, { backgroundColor: isOnline ? Colors.success : Colors.error }]}
                  onPress={toggleOnlineStatus}
                >
                  <Icon
                    name={isOnline ? "wifi" : "wifi-off"}
                    size={responsiveFontSize(2)}
                    color={Colors.white}
                  />
                  <Text style={styles.statusText}>
                    {isOnline ? 'Online' : 'Offline'}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.notificationButton}
                  onPress={() => navigation.navigate('Notifications')}
                >
                  <Icon name="bell-outline" size={responsiveFontSize(2.5)} color={Colors.white} />
                  <View style={styles.notificationDot} />
                </TouchableOpacity>
              </View>
            </View>
          </SafeAreaView>
        </LinearGradient>
        
        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          <View style={styles.statsCard}>
            <Icon name="calendar-today" size={responsiveFontSize(2.5)} color={Colors.primary} />
            <Text style={styles.statsNumber}>8</Text>
            <Text style={styles.statsLabel}>Today's Tasks</Text>
          </View>
          <View style={styles.statsCard}>
            <Icon name="clock-outline" size={responsiveFontSize(2.5)} color={Colors.pendingColor} />
            <Text style={styles.statsNumber}>3</Text>
            <Text style={styles.statsLabel}>Accepted</Text>
          </View>
          <View style={styles.statsCard}>
            <Icon name="check-circle" size={responsiveFontSize(2.5)} color={Colors.success} />
            <Text style={styles.statsNumber}>5</Text>
            <Text style={styles.statsLabel}>Completed</Text>
          </View>
        </View>
      </View>

      <SectionList
        sections={sections}
        keyExtractor={(item) => item.id}
        renderItem={renderTaskItem}
        renderSectionHeader={({ section: { title, data } }) => (
          data.length > 0 ? <Text style={styles.listTitle}>{title}</Text> : null
        )}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="clipboard-text-outline" size={responsiveFontSize(8)} color={Colors.textSecondary} />
            <Text style={styles.emptyListText}>No tasks available for today</Text>
            <Text style={styles.emptySubText}>Check back later for new assignments</Text>
          </View>
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
      
      {/* Confirmation Modal */}
      <ConfirmationModal
        visible={confirmationModal.visible}
        type={confirmationModal.type}
        title={
          confirmationModal.type === 'accept' ? 'Accept Task' :
          confirmationModal.type === 'reject' ? 'Decline Task' :
          confirmationModal.type === 'payment' ? 'Collect Payment' :
          confirmationModal.type === 'pickup' ? 'Confirm Sample Collected' : 'Confirm Action'
        }
        message={
          confirmationModal.type === 'accept'
            ? `Are you sure you want to accept the task for ${confirmationModal.taskName}?`
            : confirmationModal.type === 'reject'
            ? `Are you sure you want to decline the task for ${confirmationModal.taskName}? This action cannot be undone.`
            : confirmationModal.type === 'payment'
            ? `Please collect the payment of ₹${confirmationModal.totalAmount?.toLocaleString()} from ${confirmationModal.taskName} before proceeding with sample collection.`
            : confirmationModal.type === 'pickup'
            ? `Confirm that you have successfully picked up the blood sample from ${confirmationModal.taskName}.`
            : 'Are you sure you want to perform this action?'
        }
        confirmText={
          confirmationModal.type === 'accept' ? 'Accept' :
          confirmationModal.type === 'reject' ? 'Decline' :
          confirmationModal.type === 'payment' ? 'Payment Collected' :
          confirmationModal.type === 'pickup' ? 'Confirm Pickup' : 'Confirm'
        }
        onConfirm={confirmAction}
        onCancel={hideConfirmation}
        paymentStatus={confirmationModal.paymentStatus}
        paymentMethod={confirmationModal.paymentMethod}
        totalAmount={confirmationModal.totalAmount}
      />
    </View>
  )
}

export default Home

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  headerContainer: {
    backgroundColor: Colors.background,
  },
  headerGradient: {
    paddingHorizontal: responsiveWidth(5),
    paddingBottom: responsiveHeight(2),
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: responsiveHeight(1),
  },
  userSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    marginRight: responsiveWidth(3),
  },
  greetingContainer: {
    flex: 1,
  },
  greetingText: {
    fontSize: responsiveFontSize(1.6),
    color: 'rgba(255,255,255,0.8)',
    fontWeight: '500',
  },
  userNameText: {
    fontSize: responsiveFontSize(2.2),
    fontWeight: 'bold',
    color: Colors.white,
    marginTop: responsiveHeight(0.2),
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(3),
  },
  searchButton: {
    width: responsiveWidth(10),
    height: responsiveWidth(10),
    borderRadius: responsiveWidth(5),
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationButton: {
    width: responsiveWidth(10),
    height: responsiveWidth(10),
    borderRadius: responsiveWidth(5),
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  notificationDot: {
    position: 'absolute',
    top: responsiveWidth(2),
    right: responsiveWidth(2),
    width: responsiveWidth(2.5),
    height: responsiveWidth(2.5),
    borderRadius: responsiveWidth(1.25),
    backgroundColor: '#FF4757',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: responsiveWidth(5),
    marginTop: responsiveHeight(-1),
    gap: responsiveWidth(3),
  },
  statsCard: {
    flex: 1,
    backgroundColor: Colors.white,
    borderRadius: responsiveWidth(4),
    padding: responsiveWidth(4),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  statsNumber: {
    fontSize: responsiveFontSize(2.8),
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginTop: responsiveHeight(0.5),
  },
  statsLabel: {
    fontSize: responsiveFontSize(1.3),
    color: Colors.textSecondary,
    fontWeight: '500',
    marginTop: responsiveHeight(0.3),
    textAlign: 'center',
  },
  listContent: {
    paddingHorizontal: responsiveWidth(5),
    paddingTop: responsiveHeight(2),
    paddingBottom: responsiveHeight(2),
  },
  listTitle: {
    fontSize: responsiveFontSize(2),
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginTop: responsiveHeight(2),
    marginBottom: responsiveHeight(1.5),
  },
  taskCard: {
    backgroundColor: Colors.surface,
    borderRadius: responsiveWidth(4),
    padding: responsiveWidth(4),
    marginBottom: responsiveHeight(2),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: responsiveHeight(1.5),
  },
  patientInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarSmall: {
    width: responsiveWidth(12),
    height: responsiveWidth(12),
    borderRadius: responsiveWidth(6),
    backgroundColor: `${Colors.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: responsiveWidth(3),
  },
  patientDetails: {
    flex: 1,
  },
  patientName: {
    fontSize: responsiveFontSize(1.9),
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: responsiveHeight(0.3),
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  taskTime: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.primary,
    fontWeight: '600',
    marginLeft: responsiveWidth(1),
  },
  taskDate: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.textSecondary,
    fontWeight: '500',
    marginLeft: responsiveWidth(1),
  },
  statusBadge: {
    paddingHorizontal: responsiveWidth(3),
    paddingVertical: responsiveHeight(0.5),
    borderRadius: responsiveWidth(3),
  },
  statusText: {
    fontSize: responsiveFontSize(1.3),
    fontWeight: '600',
    textTransform: 'capitalize',
    width: responsiveWidth(13),
    textAlign: 'center',
  },
  taskDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: responsiveHeight(2),
  },
  taskAddress: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.textSecondary,
    marginLeft: responsiveWidth(2),
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: responsiveWidth(3),
  },
  acceptButton: {
    backgroundColor: Colors.primary,
    paddingVertical: responsiveHeight(1.3),
    paddingHorizontal: responsiveWidth(4),
    borderRadius: responsiveWidth(3),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    gap: responsiveWidth(2),
  },
  acceptButtonText: {
    color: Colors.white,
    fontSize: responsiveFontSize(1.7),
    fontWeight: 'bold',
  },
  declineButton: {
    backgroundColor: Colors.white,
    paddingVertical: responsiveHeight(1.3),
    paddingHorizontal: responsiveWidth(4),
    borderRadius: responsiveWidth(3),
    borderWidth: 1,
    borderColor: Colors.error,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    gap: responsiveWidth(2),
  },
  declineButtonText: {
    color: Colors.error,
    fontSize: responsiveFontSize(1.7),
    fontWeight: 'bold',
  },
  acceptedContainer: {
    // flexDirection: 'row',
    // justifyContent: 'space-between',
    // alignItems: 'center',
    backgroundColor: `${Colors.success}15`,
    paddingVertical: responsiveHeight(1.5),
    paddingHorizontal: responsiveWidth(3),
    borderRadius: responsiveWidth(3),
  },
  acceptedLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(2),
    marginBottom: responsiveHeight(1),
  },
  acceptedText: {
    color: Colors.success,
    fontWeight: 'bold',
    fontSize: responsiveFontSize(1.6),
  },
  actionButtons: {
    flexDirection: 'row',
    gap: responsiveWidth(2),
  },
  goButton: {
    backgroundColor: Colors.success,
    paddingVertical: responsiveHeight(1.3),
    paddingHorizontal: responsiveWidth(4),
    borderRadius: responsiveWidth(3),
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(2),
    flex: 1,
    justifyContent: 'center',
  },
  pickupButton: {
    backgroundColor: Colors.primary,
    paddingVertical: responsiveHeight(1.3),
    paddingHorizontal: responsiveWidth(4),
    borderRadius: responsiveWidth(3),
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(2),
    flex: 1,
    justifyContent: 'center',
  },
  goButtonText: {
    color: Colors.white,
    fontSize: responsiveFontSize(1.6),
    fontWeight: 'bold',
  },
  emptyContainer: {
    alignItems: 'center',
    marginTop: responsiveHeight(8),
    paddingHorizontal: responsiveWidth(8),
  },
  emptyListText: {
    textAlign: 'center',
    marginTop: responsiveHeight(2),
    fontSize: responsiveFontSize(2.2),
    color: Colors.textPrimary,
    fontWeight: '600',
  },
  emptySubText: {
    textAlign: 'center',
    marginTop: responsiveHeight(1),
    fontSize: responsiveFontSize(1.6),
    color: Colors.textSecondary,
  },
  testDetailCard: {
    backgroundColor: Colors.background,
    borderRadius: responsiveWidth(3),
    padding: responsiveWidth(3),
    marginBottom: responsiveHeight(1.5),
    borderLeftWidth: 3,
    borderLeftColor: Colors.primary,
  },
  testHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: responsiveHeight(1),
  },
  testName: {
    fontSize: responsiveFontSize(1.7),
    fontWeight: 'bold',
    color: Colors.textPrimary,
    flex: 1,
  },
  testCode: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.primary,
    fontWeight: '600',
    backgroundColor: `${Colors.primary}15`,
    paddingHorizontal: responsiveWidth(2),
    paddingVertical: responsiveHeight(0.3),
    borderRadius: responsiveWidth(1.5),
  },
  testInfo: {
    gap: responsiveHeight(0.8),
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: responsiveWidth(2),
  },
  infoText: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.textSecondary,
    flex: 1,
    lineHeight: responsiveHeight(2.2),
  },
  fastingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(1.5),
    backgroundColor: `${Colors.warning}15`,
    paddingHorizontal: responsiveWidth(2.5),
    paddingVertical: responsiveHeight(0.5),
    borderRadius: responsiveWidth(2),
    alignSelf: 'flex-start',
  },
  fastingText: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.warning,
    fontWeight: '600',
  },
  specialityRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(2),
  },
  specialityText: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.success,
    fontWeight: '600',
  },
  containerSection: {
    marginTop: responsiveHeight(0.5),
  },
  containerLabel: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.textPrimary,
    fontWeight: '600',
    marginBottom: responsiveHeight(0.5),
  },
  containerList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: responsiveWidth(2),
  },
  containerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(1.5),
    backgroundColor: Colors.surface,
    paddingHorizontal: responsiveWidth(2),
    paddingVertical: responsiveHeight(0.4),
    borderRadius: responsiveWidth(1.5),
    borderWidth: 1,
    borderColor: Colors.gray + '30',
  },
  colorDot: {
    width: responsiveWidth(3),
    height: responsiveWidth(3),
    borderRadius: responsiveWidth(1.5),
  },
  containerName: {
    fontSize: responsiveFontSize(1.3),
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  testSummary: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: responsiveWidth(2),
    marginBottom: responsiveHeight(1.5),
    paddingVertical: responsiveHeight(0.8),
    paddingHorizontal: responsiveWidth(3),
    backgroundColor: `${Colors.info}10`,
    borderRadius: responsiveWidth(2),
  },
  testCount: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.info,
    fontWeight: '600',
  },
  testDetailsTitle: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: responsiveHeight(1.5),
  },
  expandedContent: {
    // marginTop: responsiveHeight(1),
    paddingTop: responsiveHeight(0.8),
    borderTopWidth: 1,
    borderTopColor: Colors.gray + '30',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(2),
  },
  expandButton: {
    padding: responsiveWidth(1),
  },
  paymentContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: responsiveHeight(1.5),
    paddingHorizontal: responsiveWidth(1),
  },
  amountSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(2),
    paddingHorizontal: responsiveWidth(1),
  },
  amountText: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: 'bold',
    color: Colors.black,
  },
  paymentMethodBadge: {
    paddingHorizontal: responsiveWidth(2),
    paddingVertical: responsiveHeight(0.3),
    borderRadius: responsiveWidth(2),
  },
  paymentMethodText: {
    fontSize: responsiveFontSize(1.2),
    fontWeight: '600',
  },
  paymentStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(1),
    paddingHorizontal: responsiveWidth(2.5),
    paddingVertical: responsiveHeight(0.5),
    borderRadius: responsiveWidth(2.5),
  },
  paymentStatusText: {
    fontSize: responsiveFontSize(1.3),
    fontWeight: '600',
  },
})
