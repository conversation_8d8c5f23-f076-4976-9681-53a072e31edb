import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Image, PermissionsAndroid, ToastAndroid, ActivityIndicator } from 'react-native'
import React, { useEffect, useState } from 'react'
import InputBox from '../../components/InputBox'
import CustumBtn from '../../components/CustumBtn'
import CustomDropdown from '../../components/CustomDropdown'
import { responsiveWidth, responsiveHeight, responsiveFontSize } from 'react-native-responsive-dimensions'
import DateTimePicker from '@react-native-community/datetimepicker'
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Colors from '../../styles/Colors'
import PickImageComponent from '../../components/PickImageComponent'
import { Checkbox } from 'react-native-paper'
import BottomLink from '../../components/BottomLink'
import Geolocation from '@react-native-community/geolocation'
import DeviceInfo from 'react-native-device-info'

const genderOptions = [
  { label: 'Male', value: 'male' },
  { label: 'Female', value: 'female' },
  { label: 'Transgender', value: 'transgender' },
]

const bloodGroupOptions = [
  { label: 'A+', value: 'A+' },
  { label: 'A-', value: 'A-' },
  { label: 'B+', value: 'B+' },
  { label: 'B-', value: 'B-' },
  { label: 'O+', value: 'O+' },
  { label: 'O-', value: 'O-' },
  { label: 'AB+', value: 'AB+' },
  { label: 'AB-', value: 'AB-' },
]

const stateOptions = [
  { label: 'Maharashtra', value: 'maharashtra' },
  { label: 'Gujarat', value: 'gujarat' },
  { label: 'Delhi', value: 'delhi' },
  // Add more states as needed
]

const cityOptions = [
  { label: 'Mumbai', value: 'mumbai' },
  { label: 'Pune', value: 'pune' },
  { label: 'Ahmedabad', value: 'ahmedabad' },
  { label: 'Delhi', value: 'delhi' },
  // Add more cities as needed
]

const workLocationOptions = [
  { label: 'Mumbai', value: 'mumbai' },
  { label: 'Pune', value: 'pune' },
  { label: 'Ahmedabad', value: 'ahmedabad' },
  { label: 'Delhi', value: 'delhi' },
  // Add more as needed
]

const SignUp = ({navigation}) => {
  const [profileImage, setProfileImage] = useState(null);
  const [fullName, setFullName] = useState('')
  const [mobile, setMobile] = useState('')
  const [email, setEmail] = useState('')
  const [dob, setDob] = useState(null)
  const [gender, setGender] = useState(null)
  const [showDatePicker, setShowDatePicker] = useState(false)
  const [bloodGroup, setBloodGroup] = useState(null)
  const [city, setCity] = useState('')
  const [state, setState] = useState('')
  const [currentAddress, setCurrentAddress] = useState('')
  const [permanentAddress, setPermanentAddress] = useState('')
  const [sameAsCurrent, setSameAsCurrent] = useState(false);
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')

  const [workAreaState, setWorkAreaState] = useState('')
  const [workAreaCity, setWorkAreaCity] = useState('')
  const [workAreaPinCode, setWorkAreaPinCode] = useState('')
  const [location, setLocation] = useState(null)
  const [deviceInfo, setDeviceInfo] = useState('')
  const [loadLocation, setLoadLocation] = useState(false)

  const [aadharNumber, setAadharNumber] = useState('')
  const [panNumber, setPanNumber] = useState('')
  const [aadharDocument, setAadharDocument] = useState(null)
  const [aadharBack, setAadharBack] = useState(null)
  const [panDocument, setPanDocument] = useState(null)


  const handlePickImage = async () => {
    const image = await PickImageComponent();
    if (image) {
      console.log("Selected image object:", image);
      setProfileImage(image.uri);
      // You can also keep base64 or full file object if needed
    }
  }

  // Location permission
  const requestLocationPermission = async () => {
    console.log('Requesting location permission...');
    try {
      // Ask both fine and coarse location
      const granted = await PermissionsAndroid.requestMultiple([
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
        PermissionsAndroid.PERMISSIONS.ACCESS_BACKGROUND_LOCATION,
      ]);

      const fine = granted[PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION] === PermissionsAndroid.RESULTS.GRANTED;
      const coarse = granted[PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION] === PermissionsAndroid.RESULTS.GRANTED;

      if (fine || coarse) {
        console.log("permission granted")
        getCurrentPosition();
        
      } else {
        showToast('Please enable location permission in settings.');
        setLoadLocation(false);
      }
    } catch (err) {
      console.warn(err);
      showToast('Permission error');
    }
  }

  const getCurrentPosition = () => {
    console.log('Fetching current location...');
    setLoadLocation(true);

    Geolocation.getCurrentPosition(
      (position) => {
        console.log('Location:', position.coords);
        setLocation(position.coords);
        setLoadLocation(false);
      },
      (error) => {
        console.log('GetCurrentPosition Error:', error);
        console.log(`❌ GetCurrentPosition Error [code ${error.code}]:`, error.message);

        if (error.code === 1) {
          showToast('Permission denied.');
        } else if (error.code === 2) {
          showToast('Location unavailable. Turn on GPS.');
        } else if (error.code === 3) {
          // showToast('Location timeout. Try again.');
        } else {
          // showToast(error.message || 'Unknown location error');
        }
        useWatchPositionFallback()
        // setLoading(false);
      },
      {
        enableHighAccuracy: false,
        timeout: 20000,
        maximumAge: 10000,
        forceRequestLocation: true,
        showLocationDialog: true,
      }
    );
  };

  // fallback using watchPosition
  const useWatchPositionFallback = () => {
    const watchId = Geolocation.watchPosition(
      (position) => {
        console.log('✅ Fallback position:', position.coords);
        setLocation(position.coords);
        Geolocation.clearWatch(watchId);
        // navigation.reset({ index: 0, routes: [{ name: 'Dashboard' }] })
        setLoadLocation(false);
      },
      (error) => {
        console.log('❌ Fallback error:', error);
        showToast('Unable to fetch location');
        setLoadLocation(false);
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 0,
        interval: 5000,
        fastestInterval: 2000,
      }
    );
  };

  useEffect(() => {
    const fetchVersion = async () => {
            try {
                const version = await DeviceInfo.getVersion();
                setDeviceInfo(version);
                console.log('App Version:', version);
            } catch (error) {
                console.log('Error fetching version:', error);
            }
    };
    fetchVersion();
    // getToken();
  }, []);

  const showToast = (msg) => {
    ToastAndroid.showWithGravity(msg, ToastAndroid.LONG, ToastAndroid.CENTER);
  };

  const toggleSameAsCurrent = () => {
    const newValue = !sameAsCurrent;
    setSameAsCurrent(newValue);
    if (newValue) {
      setPermanentAddress(currentAddress);
    } else {
      setPermanentAddress('');
    }
  };

  const handlePickDocument = async (documentType) => {
    try {
      const document = await PickImageComponent();
      if (document) {
        switch (documentType) {
          case 'image':
            setProfileImage(document.uri);
            console.log('Selected image:', document.uri);
            break;
          case 'aadhar':
            setAadharDocument(document);
            console.log('Selected aadhar:', document.uri);
            break;  
          case 'aadharBack':
            setAadharBack(document);
            console.log('Selected aadharBack:', document.uri);
            break;
          case 'pan':
            setPanDocument(document);
            console.log('Selected pan:', document);
            break;
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image');
    }
  }

   const DocumentUploadBox = ({ label, document, onPress, icon = "file-document-outline" }) => (
    <TouchableOpacity onPress={onPress} style={styles.documentUploadBox}>
      <View style={styles.documentUploadContent}>
        <MaterialCommunityIcons name={icon} size={responsiveFontSize(2.5)} color={Colors.primary} />
        <View style={styles.documentUploadText}>
          <Text style={styles.documentUploadLabel}>{label}</Text>
          <Text style={styles.documentUploadStatus}>
            {document ? document.fileName || 'Document Selected' : 'Tap to upload'}
          </Text>
        </View>
        <MaterialCommunityIcons 
          name={document ? "check-circle" : "upload"} 
          size={responsiveFontSize(2)} 
          color={document ? Colors.green : Colors.gray} 
        />
      </View>
    </TouchableOpacity>
  );


  return (
    <ScrollView contentContainerStyle={styles.container} keyboardShouldPersistTaps="handled">
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle}>
          Create Account
        </Text>
        <Text style={styles.headerSubtitle}>
          Please fill in the details below to sign up.
        </Text>
      </View>

      <Text style={[styles.headerSubtitle, { textAlign: 'left', color: Colors.primary }]}>
        Personal Information:
      </Text>
      <View style={styles.formContainer}>
        <TouchableOpacity onPress={() => handlePickDocument('image')} style={styles.imageWrapper}>
            <Image source={profileImage ? { uri: profileImage } : null} style={styles.profileImage} iconName="account" iconSize={7} />
            <View style={styles.imageOverlay}>
              <MaterialCommunityIcons name="camera" color={Colors.white} size={responsiveFontSize(2.5)} />
            </View>
        </TouchableOpacity>

        <InputBox
          label="Full Name"
          placeholder="Enter Full Name"
          value={fullName}
          onChangeText={setFullName}
          leftIcon="account"
        />
        <InputBox
          label="Mobile"
          placeholder="Enter Mobile Number"
          value={mobile}
          onChangeText={setMobile}
          keyboardType="phone-pad"
          leftIcon="phone"
        />
        <InputBox
          label="Email"
          placeholder="Enter Email Id"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          leftIcon="email"
        />

        <TouchableOpacity onPress={() => setShowDatePicker(true)} style={styles.dobTouchable}>
            <InputBox
              label="DOB"
              placeholder="Date of Birth"
              value={dob ? dob.toLocaleDateString() : null}
              editable={false}
              pointerEvents="none"
              leftIcon="calendar"
            />
        </TouchableOpacity>
        {showDatePicker && (
            <DateTimePicker
              value={dob || new Date()}
              mode="date"
              display="default"
              onChange={(event, selectedDate) => {
                setShowDatePicker(false)
                selectedDate && setDob(selectedDate)
              }}
              maximumDate={new Date()}
            />
        )}

        <View style={[styles.flexView, { gap: responsiveWidth(2) }]}>
          <View style={{ flex: 1 }}>
            <CustomDropdown
              label="Gender"
              data={genderOptions}
              value={gender}
              onChange={setGender}
              placeholder="Select Gender"
              leftIcon="gender-male-female"
              search={false}
            />
          </View>
          <View style={{ flex: 1 }}>
            <CustomDropdown
              label="Blood Group"
              data={bloodGroupOptions}
              value={bloodGroup}
              onChange={setBloodGroup}
              placeholder="Blood Group"
              leftIcon="water"
              search={false}
            />
          </View>
        </View>

        <View style={[styles.flexView, { gap: responsiveWidth(2) }]}>
          <View style={{ flex: 1 }}>
            <CustomDropdown
              label="State"
              data={stateOptions}
              value={state}
              onChange={setState}
              placeholder="Select State"
              leftIcon="map"
            />
          </View>
          <View style={{ flex: 1 }}>
            <CustomDropdown
              label="City"
              data={cityOptions}
              value={city}
              onChange={setCity}
              placeholder="Select City"
              leftIcon="city"
            />
          </View>
        </View>
        <InputBox
          label="Current Address"
          placeholder="Address"
          value={currentAddress}
          onChangeText={(text) => {
            setCurrentAddress(text);
            if (sameAsCurrent) setPermanentAddress(text);
          }}
          // multiline
          leftIcon="home"
        />
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Checkbox onPress={toggleSameAsCurrent}  status={sameAsCurrent ? 'checked' : 'unchecked'} color={Colors.primary} />
          <Text style={styles.checkBoxText}>Same as Current Address</Text>
        </View>
        <InputBox
          label="Permanent Address"
          placeholder="Address"
          value={permanentAddress}
          onChangeText={setPermanentAddress}
          editable={!sameAsCurrent}
          // multiline
          leftIcon="home"
        />
      </View>

      <Text style={[styles.headerSubtitle, { textAlign: 'left', color: Colors.primary }]}>
        Set Password:
      </Text>
      <View style={styles.formContainer}>
        <InputBox
          label="Password"
          placeholder="Enter Password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          leftIcon="lock"
        />
        <InputBox
          label="Confirm Password"
          placeholder="Confirm Password"
          value={confirmPassword}
          onChangeText={setConfirmPassword}
          secureTextEntry
          leftIcon="lock-check"
        />
      </View>   


      <Text style={[styles.headerSubtitle, { textAlign: 'left', color: Colors.primary }]}>
        Set Work Location:
      </Text>
      <View style={styles.formContainer}> 
        <View style={styles.fieldCmn}>
            <View style={[styles.fieldLeft,{flex:2.1}]}>
              <CustomDropdown label="Work State" data={stateOptions} value={workAreaState} onChange={setWorkAreaState} placeholder="Work State" leftIcon="map" />
            </View>
            <View style={[styles.fieldRight,{flex:2.1}]}>
              <CustomDropdown  label="Work City" data={cityOptions} value={workAreaCity} onChange={setWorkAreaCity} placeholder="Work City" leftIcon="city" />
            </View>
        </View>

        <InputBox
          label="Work Area PinCode"
          value={workAreaPinCode}
          onChangeText={setWorkAreaPinCode}
          leftIcon="map-marker-radius"
          placeholder="Pincode"
        />

        <View style={styles.fieldCmn}>
                <View style={styles.fieldLeft}>
                  <InputBox label="Your Location" leftIcon="map-marker" keyboardType={'number-pad'} editable={false} value={location ? `${location.latitude}, ${location.longitude}` : ''} onChangeText={(text) => console.log(text)} />
                </View>
                {loadLocation ? (
                    <View style={{ flex: 1, }}>
                      <ActivityIndicator size={responsiveFontSize(2.2)} color={Colors.primary} />
                    </View>
                  ) : 
                    <TouchableOpacity onPress={()=>requestLocationPermission()} style={{flex:1.2 ,flexDirection:'row', alignItems:'center', gap:responsiveWidth(2),}}>
                      <MaterialCommunityIcons name="crosshairs-gps" size={responsiveFontSize(2.2)} color={Colors.primary} />
                      <Text style={{fontSize:responsiveFontSize(1.6), color:Colors.primary}}>Get Location</Text>
                    </TouchableOpacity>
                }
        </View>
      </View>

      <Text style={[styles.headerSubtitle, { textAlign: 'left', color: Colors.primary }]}>
        Identification & Verification:
      </Text>
      <View style={styles.formContainer}>
        <InputBox
              label="Aadhar Card Number"
              value={aadharNumber}
              onChangeText={setAadharNumber}
              leftIcon="card-account-details-outline"
              placeholder="Enter 12-digit Aadhar number"
              keyboardType="numeric"
              maxLength={12}
            />

            <DocumentUploadBox
              label="Upload Aadhar Front (PDF/JPG)"
              document={aadharDocument}
              onPress={() => handlePickDocument('aadhar')}
              icon="card-account-details"
            />

            <DocumentUploadBox
              label="Upload Aadhar Back (PDF/JPG)"
              document={aadharBack}
              onPress={() => handlePickDocument('aadharBack')}
              icon="card-account-details"
            />

            <InputBox
              label="PAN Card Number"
              value={panNumber}
              onChangeText={setPanNumber}
              leftIcon="credit-card-outline"
              placeholder="Enter PAN number"
              autoCapitalize="characters"
              maxLength={10}
            />

            <DocumentUploadBox
              label="Upload PAN Copy (PDF/JPG)"
              document={panDocument}
              onPress={() => handlePickDocument('pan')}
              icon="credit-card"
            />
      </View>

      <CustumBtn title="Sign Up" onPress={() => { /* Handle sign up logic */ }} />

      <View style={{ alignSelf: 'center', marginTop: responsiveHeight(2) }} >
        <BottomLink text={'Already have an account? '} subText={'Login'} subTextColor={Colors.green} navigation={navigation} onPress={()=>  navigation.goBack('')} />
      </View>
    </ScrollView>
  )
}

export default SignUp
const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: responsiveWidth(4),
    paddingVertical: responsiveHeight(3),
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: responsiveHeight(2),
  },
  headerTitle: {
    fontSize: responsiveHeight(3),
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: responsiveHeight(1),
  },
  headerSubtitle: {
    fontSize: responsiveHeight(2),
    color: Colors.textSecondary,
    marginBottom: responsiveHeight(1),
    textAlign: 'center',
  },
  formContainer: {
    backgroundColor: Colors.surface,
    borderRadius: responsiveFontSize(1),
    padding: responsiveHeight(2),
    shadowColor: Colors.black,
    shadowOpacity: 0.08,
    shadowRadius: responsiveFontSize(1),
    elevation: 2,
    marginBottom: responsiveHeight(2),
    gap: responsiveHeight(1.5),
  },
  flexView: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  dobTouchable: {
    // marginVertical: responsiveHeight(1),
    flex: 1
  },
  imageWrapper: {
    alignSelf: 'center',
    marginBottom: responsiveHeight(2),
    position: 'relative',
  },
  profileImage: {
    width: responsiveWidth(30),
    height: responsiveWidth(30),
    borderRadius: responsiveWidth(15),
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary,
    borderRadius: 20,
    padding: 6,
    borderWidth: 2,
    borderColor: Colors.white,
  },
  checkBoxText: {
    color: Colors.primary,
    fontWeight: '500',
    fontSize: responsiveFontSize(1.3),
  },
  fieldCmn:{
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: responsiveWidth(2),
    marginTop: responsiveHeight(0.5)
  },
  fieldLeft:{
    flex:2,
  },
  fieldRight:{
    flex:2,
  },

   documentUploadBox: {
    borderWidth: 1,
    borderColor: Colors.gray,
    borderRadius: 8,
    backgroundColor: Colors.surface,
    marginBottom: responsiveHeight(1),
  },
  documentUploadContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: responsiveWidth(4),
    gap: responsiveWidth(3),
  },
  documentUploadText: {
    flex: 1,
  },
  documentUploadLabel: {
    fontSize: responsiveFontSize(1.6),
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  documentUploadStatus: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.textSecondary,
  },
})