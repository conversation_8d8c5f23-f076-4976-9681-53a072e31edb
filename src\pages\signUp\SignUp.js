import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Image } from 'react-native'
import React, { useState } from 'react'
import InputBox from '../../components/InputBox'
import CustumBtn from '../../components/CustumBtn'
import CustomDropdown from '../../components/CustomDropdown'
import { responsiveWidth, responsiveHeight, responsiveFontSize } from 'react-native-responsive-dimensions'
import DateTimePicker from '@react-native-community/datetimepicker'
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Colors from '../../styles/Colors'
import PickImageComponent from '../../components/PickImageComponent'
import { Checkbox } from 'react-native-paper'
import BottomLink from '../../components/BottomLink'

const genderOptions = [
  { label: 'Male', value: 'male' },
  { label: 'Female', value: 'female' },
  { label: 'Transgender', value: 'transgender' },
]

const bloodGroupOptions = [
  { label: 'A+', value: 'A+' },
  { label: 'A-', value: 'A-' },
  { label: 'B+', value: 'B+' },
  { label: 'B-', value: 'B-' },
  { label: 'O+', value: 'O+' },
  { label: 'O-', value: 'O-' },
  { label: 'AB+', value: 'AB+' },
  { label: 'AB-', value: 'AB-' },
]

const stateOptions = [
  { label: 'Maharashtra', value: 'maharashtra' },
  { label: 'Gujarat', value: 'gujarat' },
  { label: 'Delhi', value: 'delhi' },
  // Add more states as needed
]

const cityOptions = [
  { label: 'Mumbai', value: 'mumbai' },
  { label: 'Pune', value: 'pune' },
  { label: 'Ahmedabad', value: 'ahmedabad' },
  { label: 'Delhi', value: 'delhi' },
  // Add more cities as needed
]

const workLocationOptions = [
  { label: 'Mumbai', value: 'mumbai' },
  { label: 'Pune', value: 'pune' },
  { label: 'Ahmedabad', value: 'ahmedabad' },
  { label: 'Delhi', value: 'delhi' },
  // Add more as needed
]

const SignUp = ({navigation}) => {
  const [profileImage, setProfileImage] = useState(null);
  const [fullName, setFullName] = useState('')
  const [mobile, setMobile] = useState('')
  const [email, setEmail] = useState('')
  const [dob, setDob] = useState(null)
  const [gender, setGender] = useState(null)
  const [showDatePicker, setShowDatePicker] = useState(false)
  const [bloodGroup, setBloodGroup] = useState(null)
  const [city, setCity] = useState('')
  const [state, setState] = useState('')
  const [currentAddress, setCurrentAddress] = useState('')
  const [permanentAddress, setPermanentAddress] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [workLocation, setWorkLocation] = useState('')
  const [sameAsCurrent, setSameAsCurrent] = useState(false);


  const handlePickImage = async () => {
    const image = await PickImageComponent();
    if (image) {
      console.log("Selected image object:", image);
      setProfileImage(image.uri);
      // You can also keep base64 or full file object if needed
    }
  }

  const toggleSameAsCurrent = () => {
    const newValue = !sameAsCurrent;
    setSameAsCurrent(newValue);
    if (newValue) {
      setPermanentAddress(currentAddress);
    } else {
      setPermanentAddress('');
    }
  };


  return (
    <ScrollView contentContainerStyle={styles.container} keyboardShouldPersistTaps="handled">
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle}>
          Create Account
        </Text>
        <Text style={styles.headerSubtitle}>
          Please fill in the details below to sign up.
        </Text>
      </View>

      <Text style={[styles.headerSubtitle, { textAlign: 'left', color: Colors.primary }]}>
        Personal Information:
      </Text>
      <View style={styles.formContainer}>
        <TouchableOpacity onPress={handlePickImage} style={styles.imageWrapper}>
            <Image source={profileImage ? { uri: profileImage } : null} style={styles.profileImage} iconName="account" iconSize={7} />
            <View style={styles.imageOverlay}>
              <MaterialCommunityIcons name="camera" color={Colors.white} size={responsiveFontSize(2.5)} />
            </View>
        </TouchableOpacity>

        <InputBox
          label="Full Name"
          placeholder="Enter Full Name"
          value={fullName}
          onChangeText={setFullName}
          leftIcon="account"
        />
        <InputBox
          label="Mobile"
          placeholder="Enter Mobile Number"
          value={mobile}
          onChangeText={setMobile}
          keyboardType="phone-pad"
          leftIcon="phone"
        />
        <InputBox
          label="Email"
          placeholder="Enter Email Id"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          leftIcon="email"
        />

        <TouchableOpacity onPress={() => setShowDatePicker(true)} style={styles.dobTouchable}>
            <InputBox
              label="DOB"
              placeholder="Date of Birth"
              value={dob ? dob.toLocaleDateString() : null}
              editable={false}
              pointerEvents="none"
              leftIcon="calendar"
            />
        </TouchableOpacity>
        {showDatePicker && (
            <DateTimePicker
              value={dob || new Date()}
              mode="date"
              display="default"
              onChange={(event, selectedDate) => {
                setShowDatePicker(false)
                selectedDate && setDob(selectedDate)
              }}
              maximumDate={new Date()}
            />
        )}

        <View style={[styles.flexView, { gap: responsiveWidth(2) }]}>
          <View style={{ flex: 1 }}>
            <CustomDropdown
              label="Gender"
              data={genderOptions}
              value={gender}
              onChange={setGender}
              placeholder="Select Gender"
              leftIcon="gender-male-female"
              search={false}
            />
          </View>
          <View style={{ flex: 1 }}>
            <CustomDropdown
              label="Blood Group"
              data={bloodGroupOptions}
              value={bloodGroup}
              onChange={setBloodGroup}
              placeholder="Blood Group"
              leftIcon="water"
              search={false}
            />
          </View>
        </View>

        <View style={[styles.flexView, { gap: responsiveWidth(2) }]}>
          <View style={{ flex: 1 }}>
            <CustomDropdown
              label="State"
              data={stateOptions}
              value={state}
              onChange={setState}
              placeholder="Select State"
              leftIcon="map"
            />
          </View>
          <View style={{ flex: 1 }}>
            <CustomDropdown
              label="City"
              data={cityOptions}
              value={city}
              onChange={setCity}
              placeholder="Select City"
              leftIcon="city"
            />
          </View>
        </View>
        <InputBox
          label="Current Address"
          placeholder="Address"
          value={currentAddress}
          onChangeText={(text) => {
            setCurrentAddress(text);
            if (sameAsCurrent) setPermanentAddress(text);
          }}
          // multiline
          leftIcon="home"
        />
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Checkbox onPress={toggleSameAsCurrent}  status={sameAsCurrent ? 'checked' : 'unchecked'} color={Colors.primary} />
          <Text style={styles.checkBoxText}>Same as Current Address</Text>
        </View>
        <InputBox
          label="Permanent Address"
          placeholder="Address"
          value={permanentAddress}
          onChangeText={setPermanentAddress}
          editable={!sameAsCurrent}
          // multiline
          leftIcon="home"
        />
        <InputBox
          label="Password"
          placeholder="Enter Password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          leftIcon="lock"
        />
        <InputBox
          label="Confirm Password"
          placeholder="Confirm Password"
          value={confirmPassword}
          onChangeText={setConfirmPassword}
          secureTextEntry
          leftIcon="lock-check"
        />
        {/* <CustomDropdown
          label="Interested Work Location"
          data={workLocationOptions}
          value={workLocation}
          onChange={setWorkLocation}
          placeholder="Select Work Location"
          leftIcon="office-building-marker"
        /> */}
      </View>

      <CustumBtn title="Sign Up" onPress={() => { /* Handle sign up logic */ }} />

      <View style={{ alignSelf: 'center', marginTop: responsiveHeight(2) }} >
        <BottomLink text={'Already have an account? '} subText={'Login'} subTextColor={Colors.green} navigation={navigation} onPress={()=>  navigation.goBack('')} />
      </View>
    </ScrollView>
  )
}

export default SignUp
const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: responsiveWidth(4),
    paddingVertical: responsiveHeight(3),
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: responsiveHeight(2),
  },
  headerTitle: {
    fontSize: responsiveHeight(3),
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: responsiveHeight(1),
  },
  headerSubtitle: {
    fontSize: responsiveHeight(2),
    color: Colors.textSecondary,
    marginBottom: responsiveHeight(2),
    textAlign: 'center',
  },
  formContainer: {
    backgroundColor: Colors.surface,
    borderRadius: responsiveFontSize(1),
    padding: responsiveHeight(2),
    shadowColor: Colors.black,
    shadowOpacity: 0.08,
    shadowRadius: responsiveFontSize(1),
    elevation: 2,
    marginBottom: responsiveHeight(2),
    gap: responsiveHeight(1.5),
  },
  flexView: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  dobTouchable: {
    // marginVertical: responsiveHeight(1),
    flex: 1
  },
  imageWrapper: {
    alignSelf: 'center',
    marginBottom: responsiveHeight(2),
    position: 'relative',
  },
  profileImage: {
    width: responsiveWidth(30),
    height: responsiveWidth(30),
    borderRadius: responsiveWidth(15),
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary,
    borderRadius: 20,
    padding: 6,
    borderWidth: 2,
    borderColor: Colors.white,
  },
  checkBoxText: {
    color: Colors.primary,
    fontWeight: '500',
    fontSize: responsiveFontSize(1.5),
  }
})