import { View, Text } from 'react-native'
import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'
import { NavigationContainer } from '@react-navigation/native'
import Login from '../pages/login/Login'
import Splash from '../pages/Splash'
import SignUp from '../pages/signUp/SignUp'
import BottomTabNavigation from './BottomTabNavigation'
import ChangePassword from '../pages/profile/profilePages/ChangePassword'
import EditProfile from '../pages/profile/profilePages/EditProfile'
import Notifications from '../pages/profile/profilePages/Notifications'
import Colors from '../styles/Colors'
import ForgotPassword from '../pages/forgotPassword/ForgotPassword'
import VerifyOTP from '../pages/otpVerification/VerifyOTP'

const StackNavigation = () => {
    const stack= createNativeStackNavigator()
  return (
    <NavigationContainer>
        <stack.Navigator initialRouteName='Splash' screenOptions={{headerShown:false}}>
            <stack.Screen name='Splash' component={Splash}/>
            <stack.Screen name='Login' component={Login}/>
            <stack.Screen name='SignUp' component={SignUp}/>
            <stack.Screen name='ForgotPassword' component={ForgotPassword}/>
            <stack.Screen name='VerifyOTP' component={VerifyOTP}/>
            <stack.Screen name='Main' component={BottomTabNavigation}/>
            <stack.Screen name='ChangePassword' component={ChangePassword}/>
            <stack.Screen name='EditProfile' component={EditProfile}/>
            <stack.Screen
                name='Notifications'
                component={Notifications}
                options={{
                    headerShown: true,
                    title: 'Notifications',
                    headerStyle: { backgroundColor: Colors.primary },
                    headerTintColor: Colors.white,
                }}
            />
        </stack.Navigator>
    </NavigationContainer>
  )
}

export default StackNavigation