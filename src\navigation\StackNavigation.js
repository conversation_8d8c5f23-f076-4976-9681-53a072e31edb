import { View, Text } from 'react-native'
import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'
import { NavigationContainer } from '@react-navigation/native'
import Login from '../pages/login/Login'
import Splash from '../pages/Splash'
import SignUp from '../pages/signUp/SignUp'
import BottomTabNavigation from './BottomTabNavigation'
import ChangePassword from '../pages/profile/profilePages/ChangePassword'
import EditProfile from '../pages/profile/profilePages/EditProfile'
import Notifications from '../pages/profile/profilePages/Notifications'
import ProfileManagement from '../pages/profile/profilePages/ProfileManagement'
import ProfessionalInfo from '../pages/profile/profilePages/ProfessionalInfo'
import IdentificationInfo from '../pages/profile/profilePages/IdentificationInfo'
import BankDetails from '../pages/profile/profilePages/BankDetails'
import DeviceAvailabilityInfo from '../pages/profile/profilePages/DeviceAvailabilityInfo'
import Colors from '../styles/Colors'
import ForgotPassword from '../pages/forgotPassword/ForgotPassword'
import VerifyOTP from '../pages/otpVerification/VerifyOTP'
import Transaction from '../pages/profile/profilePages/Transaction'

const StackNavigation = () => {
    const stack= createNativeStackNavigator()
  return (
    <NavigationContainer>
        <stack.Navigator initialRouteName='Splash' screenOptions={{headerShown:false}}>
            <stack.Screen name='Splash' component={Splash}/>
            <stack.Screen name='Login' component={Login}/>
            <stack.Screen name='SignUp' component={SignUp}/>
            <stack.Screen name='ForgotPassword' component={ForgotPassword}/>
            <stack.Screen name='VerifyOTP' component={VerifyOTP}/>
            <stack.Screen name='Main' component={BottomTabNavigation}/>
            <stack.Screen name='ChangePassword' component={ChangePassword}/>
            <stack.Screen name='EditProfile' component={EditProfile}/>
            <stack.Screen name='ProfileManagement' component={ProfileManagement}/>
            <stack.Screen name='Transaction' component={Transaction}/>
            <stack.Screen name='ProfessionalInfo' component={ProfessionalInfo}/>
            <stack.Screen name='IdentificationInfo' component={IdentificationInfo}/>
            <stack.Screen name='BankDetails' component={BankDetails}/>
            <stack.Screen name='DeviceAvailabilityInfo' component={DeviceAvailabilityInfo}/>
            <stack.Screen
                name='Notifications'
                component={Notifications}
                options={{
                    headerShown: true,
                    title: 'Notifications',
                    headerStyle: { backgroundColor: Colors.primary },
                    headerTintColor: Colors.white,
                }}
            />
        </stack.Navigator>
    </NavigationContainer>
  )
}

export default StackNavigation