import { View, Text, StyleSheet, StatusBar, Animated, Dimensions } from 'react-native'
import React, { useEffect, useRef } from 'react'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import LinearGradient from 'react-native-linear-gradient'
import Icon from 'react-native-vector-icons/MaterialCommunityIcons'
import Colors from '../styles/Colors'

const { width, height } = Dimensions.get('window')

const Splash = ({navigation}) => {
    const fadeAnim = useRef(new Animated.Value(0)).current
    const scaleAnim = useRef(new Animated.Value(0.3)).current
    const slideAnim = useRef(new Animated.Value(50)).current
    const pulseAnim = useRef(new Animated.Value(1)).current

    useEffect(() => {
        // Start animations
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 1000,
                useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
                toValue: 1,
                tension: 50,
                friction: 7,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 800,
                delay: 300,
                useNativeDriver: true,
            }),
        ]).start()

        // Pulse animation for the icon
        const pulseAnimation = Animated.loop(
            Animated.sequence([
                Animated.timing(pulseAnim, {
                    toValue: 1.1,
                    duration: 1000,
                    useNativeDriver: true,
                }),
                Animated.timing(pulseAnim, {
                    toValue: 1,
                    duration: 1000,
                    useNativeDriver: true,
                }),
            ])
        )

        setTimeout(() => {
            pulseAnimation.start()
        }, 1000)

        // Navigate to Login after 3 seconds
        const timer = setTimeout(() => {
            navigation.replace('Login')
        }, 3000)

        // Cleanup
        return () => {
            clearTimeout(timer)
            pulseAnimation.stop()
        }
    }, [])

    return (
        <View style={styles.container}>
            <StatusBar translucent backgroundColor="transparent" barStyle="light-content" />

            {/* Background Gradient */}
            <LinearGradient
                colors={[Colors.primary, '#0088CC', '#005599']}
                style={styles.gradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
            >
                {/* Animated Background Elements */}
                <View style={styles.backgroundElements}>
                    <Animated.View style={[styles.circle1, { opacity: fadeAnim }]} />
                    <Animated.View style={[styles.circle2, { opacity: fadeAnim }]} />
                    <Animated.View style={[styles.circle3, { opacity: fadeAnim }]} />
                </View>

                {/* Main Content */}
                <View style={styles.content}>
                    {/* Logo Section */}
                    <Animated.View
                        style={[
                            styles.logoContainer,
                            {
                                opacity: fadeAnim,
                                transform: [
                                    { scale: scaleAnim },
                                    { scale: pulseAnim }
                                ]
                            }
                        ]}
                    >
                        <View style={styles.logoBackground}>
                            <Icon name="water-outline" size={responsiveFontSize(8)} color={Colors.white} />
                        </View>
                    </Animated.View>

                    {/* App Name */}
                    <Animated.View
                        style={[
                            styles.textContainer,
                            {
                                opacity: fadeAnim,
                                transform: [{ translateY: slideAnim }]
                            }
                        ]}
                    >
                        <Text style={styles.appName}>SLD Phlebo</Text>
                        <Text style={styles.tagline}>Professional Phlebotomy Services</Text>
                    </Animated.View>

                    {/* Loading Indicator */}
                    <Animated.View
                        style={[
                            styles.loadingContainer,
                            {
                                opacity: fadeAnim,
                                transform: [{ translateY: slideAnim }]
                            }
                        ]}
                    >
                        <View style={styles.loadingBar}>
                            <Animated.View
                                style={[
                                    styles.loadingProgress,
                                    {
                                        transform: [{ scaleX: pulseAnim }]
                                    }
                                ]}
                            />
                        </View>
                        <Text style={styles.loadingText}>Loading...</Text>
                    </Animated.View>
                </View>

                {/* Footer */}
                <Animated.View
                    style={[
                        styles.footer,
                        {
                            opacity: fadeAnim,
                            transform: [{ translateY: slideAnim }]
                        }
                    ]}
                >
                    <Text style={styles.footerText}>Powered by SLD Healthcare</Text>
                    <Text style={styles.versionText}>Version 1.0.0</Text>
                </Animated.View>
            </LinearGradient>
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    gradient: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    backgroundElements: {
        position: 'absolute',
        width: '100%',
        height: '100%',
    },
    circle1: {
        position: 'absolute',
        width: responsiveWidth(80),
        height: responsiveWidth(80),
        borderRadius: responsiveWidth(40),
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        top: -responsiveWidth(20),
        right: -responsiveWidth(20),
    },
    circle2: {
        position: 'absolute',
        width: responsiveWidth(60),
        height: responsiveWidth(60),
        borderRadius: responsiveWidth(30),
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        bottom: responsiveHeight(10),
        left: -responsiveWidth(15),
    },
    circle3: {
        position: 'absolute',
        width: responsiveWidth(40),
        height: responsiveWidth(40),
        borderRadius: responsiveWidth(20),
        backgroundColor: 'rgba(255, 255, 255, 0.08)',
        top: responsiveHeight(20),
        left: responsiveWidth(10),
    },
    content: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: responsiveWidth(10),
    },
    logoContainer: {
        marginBottom: responsiveHeight(4),
    },
    logoBackground: {
        width: responsiveWidth(30),
        height: responsiveWidth(30),
        borderRadius: responsiveWidth(15),
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 8,
        },
        shadowOpacity: 0.3,
        shadowRadius: 10,
        elevation: 15,
    },
    textContainer: {
        alignItems: 'center',
        marginBottom: responsiveHeight(6),
    },
    appName: {
        fontSize: responsiveFontSize(4.5),
        fontWeight: 'bold',
        color: Colors.white,
        textAlign: 'center',
        marginBottom: responsiveHeight(1),
        letterSpacing: 2,
        textShadowColor: 'rgba(0, 0, 0, 0.3)',
        textShadowOffset: { width: 0, height: 2 },
        textShadowRadius: 4,
    },
    tagline: {
        fontSize: responsiveFontSize(1.8),
        color: 'rgba(255, 255, 255, 0.9)',
        textAlign: 'center',
        fontWeight: '300',
        letterSpacing: 1,
    },
    loadingContainer: {
        alignItems: 'center',
        width: '100%',
    },
    loadingBar: {
        width: responsiveWidth(60),
        height: responsiveHeight(0.5),
        backgroundColor: 'rgba(255, 255, 255, 0.3)',
        borderRadius: responsiveHeight(0.25),
        overflow: 'hidden',
        marginBottom: responsiveHeight(2),
    },
    loadingProgress: {
        height: '100%',
        backgroundColor: Colors.white,
        borderRadius: responsiveHeight(0.25),
        width: '70%',
    },
    loadingText: {
        fontSize: responsiveFontSize(1.6),
        color: 'rgba(255, 255, 255, 0.8)',
        fontWeight: '400',
    },
    footer: {
        position: 'absolute',
        bottom: responsiveHeight(5),
        alignItems: 'center',
    },
    footerText: {
        fontSize: responsiveFontSize(1.4),
        color: 'rgba(255, 255, 255, 0.7)',
        fontWeight: '300',
        marginBottom: responsiveHeight(0.5),
    },
    versionText: {
        fontSize: responsiveFontSize(1.2),
        color: 'rgba(255, 255, 255, 0.5)',
        fontWeight: '300',
    },
})

export default Splash