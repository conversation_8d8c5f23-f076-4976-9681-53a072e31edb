import { View, Text } from 'react-native'
import React, { useEffect } from 'react'

const Splash = ({navigation}) => {

    useEffect(()=>{
        // Simulate a splash screen delay
        const timer = setTimeout(() => {
            // Navigate to Login after 2 seconds
            navigation.replace('Login')
        }, 2000)

        // Cleanup the timer on unmount
        return () => clearTimeout(timer)
    })

  return (
    <View>
      <Text>Splash</Text>
    </View>
  )
}

export default Splash