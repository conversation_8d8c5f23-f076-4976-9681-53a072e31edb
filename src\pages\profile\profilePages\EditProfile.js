import { View, Text, StyleSheet, ScrollView, Alert, KeyboardAvoidingView, Image, TouchableOpacity, ActivityIndicator, ToastAndroid, PermissionsAndroid } from 'react-native'
import React, { useEffect, useState } from 'react'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import InputBox from '../../../components/InputBox'
import Colors from '../../../styles/Colors'
import CustomDropdown from '../../../components/CustomDropdown'
import CustumBtn from '../../../components/CustumBtn'
import MyHeader from '../../../components/MyHeader'
import PickImageComponent from '../../../components/PickImageComponent'
import { Checkbox, Switch } from 'react-native-paper'
import * as DocumentPicker from '@react-native-documents/picker';
import Geolocation from '@react-native-community/geolocation';
import DeviceInfo from 'react-native-device-info';

const EditProfile = ({navigation}) => {
  const [profileImage, setProfileImage] = useState(null);
  const [name, setName] = useState('Santosh Kumar')
  const [email, setEmail] = useState('<EMAIL>')
  const [mobile, setMobile] = useState('1234567890')
  const [dob, setDob] = useState('01/01/1990') // This can be integrated with a DatePicker
  const [gender, setGender] = useState('Male')
  const [bloodGroup, setBloodGroup] = useState('A+')
  const [state, setState] = useState('Chhattisgarh')
  const [city, setCity] = useState('Raipur')
  const [currentAddress, setCurrentAddress] = useState('Sundar Nagar, Raipur, Chhattisgarh 492001')
  const [permanentAddress, setPermanentAddress] = useState('Sundar Nagar, Raipur, Chhattisgarh 492001')
  const [load, setLoad] = useState(false)
  

  // Professional Information
  const [qualification, setQualification] = useState('')
  const [certificationDocument, setCertificationDocument] = useState(null)
  const [totalExperience, setTotalExperience] = useState('')
  const [specialSkills, setSpecialSkills] = useState('')

  // Identification & Verification
  const [aadharNumber, setAadharNumber] = useState('')
  const [panNumber, setPanNumber] = useState('')
  const [aadharDocument, setAadharDocument] = useState(null)
  const [panDocument, setPanDocument] = useState(null)

  // Bank Details
  const [bankName, setBankName] = useState('')
  const [accountHolderName, setAccountHolderName] = useState('')
  const [accountNumber, setAccountNumber] = useState('')
  const [ifscCode, setIfscCode] = useState('')
  const [bankDocument, setBankDocument] = useState(null)

  // Device & Availability Info
  const [workAreaState, setWorkAreaState] = useState('')
  const [workAreaCity, setWorkAreaCity] = useState('')
  const [workAreaPinCode, setWorkAreaPinCode] = useState('')
  const [availableDays, setAvailableDays] = useState([])
  const [timeSlots, setTimeSlots] = useState('')
  const [location, setLocation] = useState(null)
  const [gpsAccess, setGpsAccess] = useState(false)
  const [deviceInfo, setDeviceInfo] = useState('')
  const [loading, setLoading] = useState(false)

  const genderData = [
    { label: 'Male', value: 'Male' },
    { label: 'Female', value: 'female' },
    { label: 'Other', value: 'other' },
  ];

  const bloodGroupOptions = [
    { label: 'A+', value: 'A+' },
    { label: 'A-', value: 'A-' },
    { label: 'B+', value: 'B+' },
    { label: 'B-', value: 'B-' },
    { label: 'O+', value: 'O+' },
    { label: 'O-', value: 'O-' },
    { label: 'AB+', value: 'AB+' },
    { label: 'AB-', value: 'AB-' },
  ]

  const stateOptions = [
    { label: 'Chhattisgarh', value: 'Chhattisgarh' },
    { label: 'Gujarat', value: 'gujarat' },
    { label: 'Delhi', value: 'delhi' },
    // Add more states as needed
  ]

  const cityOptions = [
    { label: 'Raipur', value: 'Raipur' },
    { label: 'Pune', value: 'pune' },
    { label: 'Ahmedabad', value: 'ahmedabad' },
    { label: 'Delhi', value: 'delhi' },
    // Add more cities as needed
  ]

  const qualificationData = [
    { label: 'DMLT (Diploma in Medical Laboratory Technology)', value: 'dmlt' },
    { label: 'BMLT (Bachelor in Medical Laboratory Technology)', value: 'bmlt' },
    { label: 'BSc Medical Laboratory Technology', value: 'bsc_mlt' },
    { label: 'MSc Medical Laboratory Technology', value: 'msc_mlt' },
    { label: 'Certificate in Phlebotomy', value: 'cert_phlebotomy' },
    { label: 'Other', value: 'other' },
  ];

  const experienceData = [
    { label: '0-1 years', value: '0-1' },
    { label: '1-2 years', value: '1-2' },
    { label: '2-5 years', value: '2-5' },
    { label: '5-10 years', value: '5-10' },
    { label: '10+ years', value: '10+' },
  ];

  const daysData = [
    { label: 'Monday', value: 'monday' },
    { label: 'Tuesday', value: 'tuesday' },
    { label: 'Wednesday', value: 'wednesday' },
    { label: 'Thursday', value: 'thursday' },
    { label: 'Friday', value: 'friday' },
    { label: 'Saturday', value: 'saturday' },
    { label: 'Sunday', value: 'sunday' },
  ];

  const timeSlotData = [
    { label: 'Morning (6 AM - 12 PM)', value: 'morning' },
    { label: 'Afternoon (12 PM - 6 PM)', value: 'afternoon' },
    { label: 'Evening (6 PM - 10 PM)', value: 'evening' },
    { label: 'Full Day (6 AM - 10 PM)', value: 'fullday' },
    { label: 'Flexible', value: 'flexible' },
  ];

  const handlePickImage = async (documentType) => {
    try {
      const image = await PickImageComponent();
      if (image) {
        switch (documentType) {
          case 'profile':
            setProfileImage(image.uri);
            break;
          case 'bank':
            setBankDocument(image.uri);
            break;
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image');
    }
  }

   const handleUploadDocument = async (documentType) => {
    try {
      const res = await DocumentPicker.pick({
        type: [DocumentPicker.types.images, DocumentPicker.types.pdf],
        allowMultiSelection: false,
      });
      if (res && res.length > 0) {
        const document = res[0];
        if (document) {
          switch (documentType) {
            case 'certification':
              setCertificationDocument(document);
              break;
            case 'aadhar':
              setAadharDocument(document);
              break;
            case 'pan':
              setPanDocument(document);
              break;
          }
        }
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        console.log('User cancelled upload');
      } else {
        console.error('Upload Error:', err);
      }
    }
  };

  const handleDaySelection = (day) => {
    setAvailableDays(prev => {
      if (prev.includes(day)) {
        return prev.filter(d => d !== day);
      } else {
        return [...prev, day];
      }
    });
  };
  
  // Location permission
  const requestLocationPermission = async () => {
    console.log('Requesting location permission...');
    try {
      // Ask both fine and coarse location
      const granted = await PermissionsAndroid.requestMultiple([
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
        PermissionsAndroid.PERMISSIONS.ACCESS_BACKGROUND_LOCATION,
      ]);

      const fine = granted[PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION] === PermissionsAndroid.RESULTS.GRANTED;
      const coarse = granted[PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION] === PermissionsAndroid.RESULTS.GRANTED;

      if (fine || coarse) {
        console.log("permission granted")
        getCurrentPosition();
        
      } else {
        showToast('Please enable location permission in settings.');
        setLoading(false);
      }
    } catch (err) {
      console.warn(err);
      showToast('Permission error');
    }
  }

  const getCurrentPosition = () => {
    console.log('Fetching current location...');
    setLoading(true);

    Geolocation.getCurrentPosition(
      (position) => {
        console.log('Location:', position.coords);
        setLocation(position.coords);
        setLoading(false);
      },
      (error) => {
        console.log('GetCurrentPosition Error:', error);
        console.log(`❌ GetCurrentPosition Error [code ${error.code}]:`, error.message);

        if (error.code === 1) {
          showToast('Permission denied.');
        } else if (error.code === 2) {
          showToast('Location unavailable. Turn on GPS.');
        } else if (error.code === 3) {
          // showToast('Location timeout. Try again.');
        } else {
          // showToast(error.message || 'Unknown location error');
        }
        useWatchPositionFallback()
        // setLoading(false);
      },
      {
        enableHighAccuracy: false,
        timeout: 20000,
        maximumAge: 10000,
        forceRequestLocation: true,
        showLocationDialog: true,
      }
    );
  };

  // fallback using watchPosition
  const useWatchPositionFallback = () => {
    const watchId = Geolocation.watchPosition(
      (position) => {
        console.log('✅ Fallback position:', position.coords);
        setLocation(position.coords);
        Geolocation.clearWatch(watchId);
        // navigation.reset({ index: 0, routes: [{ name: 'Dashboard' }] })
        setLoading(false);
      },
      (error) => {
        console.log('❌ Fallback error:', error);
        showToast('Unable to fetch location');
        setLoading(false);
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 0,
        interval: 5000,
        fastestInterval: 2000,
      }
    );
  };

  // useEffect(() => {
  //   requestLocationPermission()
  // }, [])

  useEffect(() => {
    const fetchVersion = async () => {
            try {
                const version = aait DeviceInfo.getVersion();
                setDeviceInfo(version);
                console.log('App Version:', version);
            } catch (error) {
                console.log('Error fetching version:', error);
            }
    };
    fetchVersion();
    // getToken();
  }, []);

  const showToast = (msg) => {
    ToastAndroid.showWithGravity(msg, ToastAndroid.LONG, ToastAndroid.CENTER);
  };


  const DocumentUploadBox = ({ label, document, onPress, icon = "file-document-outline" }) => (
    <TouchableOpacity onPress={onPress} style={styles.documentUploadBox}>
      <View style={styles.documentUploadContent}>
        <MaterialCommunityIcons name={icon} size={responsiveFontSize(2.5)} color={Colors.primary} />
        <View style={styles.documentUploadText}>
          <Text style={styles.documentUploadLabel}>{label}</Text>
          <Text style={styles.documentUploadStatus}>
            {document ? document.fileName || 'Document Selected' : 'Tap to upload'}
          </Text>
        </View>
        <MaterialCommunityIcons
          name={document ? "check-circle" : "upload"}
          size={responsiveFontSize(2)}
          color={document ? Colors.green : Colors.gray}
        />
      </View>
    </TouchableOpacity>
  );

  const handleSaveChanges = () => {
    setLoading(true)
    // Simulate API call
    setTimeout(() => {
      setLoading(false)
      Alert.alert('Success', 'Profile updated successfully!')
    }, 2000)
  }

  return (
    <View style={styles.container}>
      <MyHeader title="Edit Profile" onBackPress={() => navigation.goBack()} />
      <KeyboardAvoidingView behavior="padding" style={{ flex: 1 }}>
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={styles.contentContainer}>
      <View style={styles.content}>
        {/* <Text style={styles.title}>Edit Profile</Text> */}
        <TouchableOpacity onPress={() => handlePickImage('profile')} style={styles.imageWrapper}>
            <Image source={profileImage ? { uri: profileImage } : null} style={styles.profileImage} iconName="account" iconSize={7} />
            <View style={styles.imageOverlay}>
              <MaterialCommunityIcons name="camera" color={Colors.white} size={responsiveFontSize(2.5)} />
            </View>
        </TouchableOpacity>

        <InputBox
          label="Full Name"
          value={name}
          onChangeText={setName}
          leftIcon="account-outline"
        />

        <InputBox
          label="Email Address"
          value={email}
          onChangeText={setEmail}
          leftIcon="email-outline"
          keyboardType="email-address"
          disabled // Email is typically not editable
        />

        <InputBox
          label="Mobile Number"
          value={mobile}
          onChangeText={setMobile}
          leftIcon="phone-outline"
          keyboardType="phone-pad"
        />

        <InputBox
          label="Date of Birth"
          value={dob}
          onChangeText={setDob}
          leftIcon="calendar-outline"
          placeholder="DD/MM/YYYY"
        />

        <View style={styles.fieldCmn}>
          <View style={styles.fieldLeft}>
            <CustomDropdown
              label="Gender"
              data={genderData}
              value={gender}
              onChange={setGender}
              leftIcon="gender-male-female"
              search={false}
            />
          </View>
          <View style={styles.fieldRight}>
            <CustomDropdown
              label="Blood Group"
              data={bloodGroupOptions}
              value={bloodGroup}
              onChange={setBloodGroup}
              leftIcon="water"
              search={false}
            />
          </View>
        </View>

        <InputBox
          label="Current Address"
          value={currentAddress}
          onChangeText={setCurrentAddress}
          leftIcon="map-marker-outline"
          placeholder="Enter current address"
          multiline
        />

        <InputBox
          label="Permanent Address"
          value={permanentAddress}
          onChangeText={setPermanentAddress}
          leftIcon="home-outline"
          placeholder="Enter permanent address"
          multiline
        />

        <View style={styles.fieldCmn}>
          <View style={styles.fieldLeft}>
            <CustomDropdown
              label="State"
              data={stateOptions}
              value={state}
              onChange={setState}
              placeholder="Select State"
              leftIcon="map"
            />
          </View>
          <View style={styles.fieldRight}>
            <CustomDropdown
              label="City"
              data={cityOptions}
              value={city}
              onChange={setCity}
              placeholder="Select City"
              leftIcon="city"
            />
          </View>
        </View>

        {/* Professional Information Section */}
        <Text style={styles.sectionTitle}>Professional Information</Text>

        <CustomDropdown
          label="Qualification"
          data={qualificationData}
          value={qualification}
          onChange={setQualification}
          leftIcon="school-outline"
          placeholder="Select your qualification"
          search={true}
        />

        <DocumentUploadBox
          label="Certification (PDF/JPG)"
          document={certificationDocument}
          onPress={() => handleUploadDocument('certification')}
          icon="certificate-outline"
        />

        <CustomDropdown
          label="Total Experience"
          data={experienceData}
          value={totalExperience}
          onChange={setTotalExperience}
          leftIcon="briefcase-outline"
          placeholder="Select experience range"
          search={false}
        />

        <InputBox
          label="Special Skills"
          value={specialSkills}
          onChangeText={setSpecialSkills}
          leftIcon="star-outline"
          placeholder="e.g., pediatric phlebotomy, geriatric care"
          multiline
        />

        {/* Identification & Verification Section */}
        <Text style={styles.sectionTitle}>Identification & Verification</Text>

        <InputBox
          label="Aadhar Card Number"
          value={aadharNumber}
          onChangeText={setAadharNumber}
          leftIcon="card-account-details-outline"
          placeholder="Enter 12-digit Aadhar number"
          keyboardType="numeric"
          maxLength={12}
        />

        <DocumentUploadBox
          label="Upload Aadhar Copy (PDF/JPG)"
          document={aadharDocument}
          onPress={() => handleUploadDocument('aadhar')}
          icon="card-account-details"
        />

        <InputBox
          label="PAN Card Number"
          value={panNumber}
          onChangeText={setPanNumber}
          leftIcon="credit-card-outline"
          placeholder="Enter PAN number"
          autoCapitalize="characters"
          maxLength={10}
        />

        <DocumentUploadBox
          label="Upload PAN Copy (PDF/JPG)"
          document={panDocument}
          onPress={() => handleUploadDocument('pan')}
          icon="credit-card"
        />

        {/* Bank Details Section */}
        <Text style={styles.sectionTitle}>Bank Details (for payout/commission)</Text>

        <InputBox
          label="Bank Name"
          value={bankName}
          onChangeText={setBankName}
          leftIcon="bank-outline"
          placeholder="Enter bank name"
        />

        <InputBox
          label="Account Holder Name"
          value={accountHolderName}
          onChangeText={setAccountHolderName}
          leftIcon="account-outline"
          placeholder="Enter account holder name"
        />

        <InputBox
          label="Account Number"
          value={accountNumber}
          onChangeText={setAccountNumber}
          leftIcon="credit-card-outline"
          placeholder="Enter account number"
          keyboardType="numeric"
        />

        <InputBox
          label="IFSC Code"
          value={ifscCode}
          onChangeText={setIfscCode}
          leftIcon="bank-transfer"
          placeholder="Enter IFSC code"
          autoCapitalize="characters"
        />

        <DocumentUploadBox
          label="Cancelled Cheque or Passbook Photo"
          document={bankDocument}
          onPress={() => handlePickImage('bank')}
          icon="file-document-outline"
        />

        {/* Device & Availability Info Section */}
        <Text style={styles.sectionTitle}>Device & Availability Info</Text>

        <View style={styles.fieldCmn}>
            <View style={[styles.fieldLeft,{flex:2.1}]}>
              <CustomDropdown label="State" data={stateOptions} value={workAreaState} onChange={setWorkAreaState} placeholder="State" leftIcon="map" />
            </View>
            <View style={[styles.fieldRight,{flex:2.1}]}>
              <CustomDropdown  label="Work City" data={cityOptions} value={workAreaCity} onChange={setWorkAreaCity} placeholder="City" leftIcon="city" />
            </View>
        </View>

        <InputBox
          label="Work Area PinCode"
          value={workAreaPinCode}
          onChangeText={setWorkAreaPinCode}
          leftIcon="map-marker-radius"
          placeholder="Pincode"
        />

        <CustomDropdown
            label="Time Slots"
            data={timeSlotData}
            value={timeSlots}
            onChange={setTimeSlots}
            leftIcon="clock-outline"
            placeholder="Select preferred time slots"
            search={false}
        />

        <Text style={styles.subSectionTitle}>Available Days</Text>
        <View style={styles.checkboxContainer}>
          {daysData.map((day) => (
            <View key={day.value} style={styles.checkboxItem}>
              <Checkbox
                status={availableDays.includes(day.value) ? 'checked' : 'unchecked'}
                onPress={() => handleDaySelection(day.value)}
                color={Colors.primary}
              />
              <Text style={styles.checkboxLabel}>{day.label}</Text>
            </View>
          ))}
        </View>

        <View style={styles.fieldCmn}>
            <View style={styles.fieldLeft}>
              <InputBox label="Your Location" icon="map-marker" keyboardType={'number-pad'} editable={false} value={location ? `${location.latitude}, ${location.longitude}` : ''} onChangeText={(text) => console.log(text)} />
            </View>
            {loading ? (
                <View style={{ flex: 1, }}>
                  <ActivityIndicator size={responsiveFontSize(2.2)} color={Colors.primary} />
                </View>
              ) : 
                <TouchableOpacity onPress={()=>requestLocationPermission()} style={{flex:1.2 ,flexDirection:'row', alignItems:'center', gap:responsiveWidth(2),}}>
                  <MaterialCommunityIcons name="crosshairs-gps" size={responsiveFontSize(2.2)} color={Colors.primary} />
                  <Text style={{fontSize:responsiveFontSize(1.6), color:Colors.primary}}>Get Location</Text>
                </TouchableOpacity>
            }
        </View>

        <CustumBtn
          title="Save Changes"
          onPress={handleSaveChanges}
          loading={load}
          disabled={load}
        />
      </View>
    </ScrollView>
    </KeyboardAvoidingView>  
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  contentContainer: {
    flexGrow: 1,
    // justifyContent: 'center',
  },
  content: {
    padding: responsiveWidth(5),
    gap:responsiveHeight(2)
  },
  title: {
    fontSize: responsiveFontSize(3),
    fontWeight: 'bold',
    marginBottom: responsiveHeight(4),
    textAlign: 'center',
    color: Colors.textPrimary,
  },
  imageWrapper: {
    alignSelf: 'center',
    marginBottom: responsiveHeight(2),
    position: 'relative',
  },
  profileImage: {
    width: responsiveWidth(30),
    height: responsiveWidth(30),
    borderRadius: responsiveWidth(15),
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary,
    borderRadius: 20,
    padding: 6,
    borderWidth: 2,
    borderColor: Colors.white,
  },
  sectionTitle: {
    fontSize: responsiveFontSize(2.2),
    fontWeight: 'bold',
    color: Colors.primary,
    marginTop: responsiveHeight(2),
    marginBottom: responsiveHeight(1),
  },
  documentUploadBox: {
    borderWidth: 1,
    borderColor: Colors.gray,
    borderRadius: 8,
    backgroundColor: Colors.surface,
    marginBottom: responsiveHeight(1),
  },
  documentUploadContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: responsiveWidth(4),
    gap: responsiveWidth(3),
  },
  documentUploadText: {
    flex: 1,
  },
  documentUploadLabel: {
    fontSize: responsiveFontSize(1.6),
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  documentUploadStatus: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.textSecondary,
  },
  subSectionTitle: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: '600',
    color: Colors.textPrimary,
    marginTop: responsiveHeight(1),
    marginBottom: responsiveHeight(0.5),
  },
  checkboxContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: responsiveHeight(1),
  },
  checkboxItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginBottom: responsiveHeight(0.5),
  },
  checkboxLabel: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.textPrimary,
    marginLeft: responsiveWidth(1),
  },
  switchContainer: {
    marginBottom: responsiveHeight(1),
  },
  switchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: responsiveWidth(4),
    backgroundColor: Colors.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.gray,
    gap: responsiveWidth(3),
  },
  switchLabel: {
    flex: 1,
    fontSize: responsiveFontSize(1.6),
    color: Colors.textPrimary,
  },
  fieldCmn:{
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: responsiveWidth(2),
    marginTop: responsiveHeight(0.5)
  },
  fieldLeft:{
    flex:2,
  },
  fieldRight:{
    flex:2,
  },
})

export default EditProfile