import { View, Text, StyleSheet, ScrollView, Alert, KeyboardAvoidingView, Image, TouchableOpacity } from 'react-native'
import React, { useState } from 'react'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import InputBox from '../../../components/InputBox'
import Colors from '../../../styles/Colors'
import CustomDropdown from '../../../components/CustomDropdown'
import CustumBtn from '../../../components/CustumBtn'
import MyHeader from '../../../components/MyHeader'
import PickImageComponent from '../../../components/PickImageComponent'
import DocumentPicker from '../../../components/DocumentPicker'

const EditProfile = ({navigation}) => {
  const [profileImage, setProfileImage] = useState(null);
  const [name, setName] = useState('<PERSON><PERSON>')
  const [email, setEmail] = useState('<EMAIL>')
  const [mobile, setMobile] = useState('1234567890')
  const [dob, setDob] = useState('01/01/1990') // This can be integrated with a DatePicker
  const [gender, setGender] = useState(null)
  const [bloodGroup, setBloodGroup] = useState('A+')
  const [state, setState] = useState('')
  const [city, setCity] = useState('')
  const [currentAddress, setCurrentAddress] = useState('')
  const [permanentAddress, setPermanentAddress] = useState('')
  const [loading, setLoading] = useState(false)

  // Professional Information
  const [qualification, setQualification] = useState('')
  const [certificationDocument, setCertificationDocument] = useState(null)
  const [totalExperience, setTotalExperience] = useState('')
  const [specialSkills, setSpecialSkills] = useState('')

  // Identification & Verification
  const [aadharNumber, setAadharNumber] = useState('')
  const [panNumber, setPanNumber] = useState('')
  const [aadharDocument, setAadharDocument] = useState(null)
  const [panDocument, setPanDocument] = useState(null)

  const genderData = [
    { label: 'Male', value: 'male' },
    { label: 'Female', value: 'female' },
    { label: 'Other', value: 'other' },
  ];

  const qualificationData = [
    { label: 'DMLT (Diploma in Medical Laboratory Technology)', value: 'dmlt' },
    { label: 'BMLT (Bachelor in Medical Laboratory Technology)', value: 'bmlt' },
    { label: 'BSc Medical Laboratory Technology', value: 'bsc_mlt' },
    { label: 'MSc Medical Laboratory Technology', value: 'msc_mlt' },
    { label: 'Certificate in Phlebotomy', value: 'cert_phlebotomy' },
    { label: 'Other', value: 'other' },
  ];

  const experienceData = [
    { label: '0-1 years', value: '0-1' },
    { label: '1-2 years', value: '1-2' },
    { label: '2-5 years', value: '2-5' },
    { label: '5-10 years', value: '5-10' },
    { label: '10+ years', value: '10+' },
  ];

  const handlePickImage = async () => {
    const image = await PickImageComponent();
    if (image) {
      setProfileImage(image.uri);
    }
  }

  const handlePickDocument = async (documentType) => {
    try {
      const document = await DocumentPicker();
      if (document) {
        switch (documentType) {
          case 'certification':
            setCertificationDocument(document);
            break;
          case 'aadhar':
            setAadharDocument(document);
            break;
          case 'pan':
            setPanDocument(document);
            break;
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick document');
    }
  }

  const DocumentUploadBox = ({ label, document, onPress, icon = "file-document-outline" }) => (
    <TouchableOpacity onPress={onPress} style={styles.documentUploadBox}>
      <View style={styles.documentUploadContent}>
        <MaterialCommunityIcons name={icon} size={responsiveFontSize(2.5)} color={Colors.primary} />
        <View style={styles.documentUploadText}>
          <Text style={styles.documentUploadLabel}>{label}</Text>
          <Text style={styles.documentUploadStatus}>
            {document ? document.fileName || 'Document Selected' : 'Tap to upload'}
          </Text>
        </View>
        <MaterialCommunityIcons
          name={document ? "check-circle" : "upload"}
          size={responsiveFontSize(2)}
          color={document ? Colors.green : Colors.gray}
        />
      </View>
    </TouchableOpacity>
  );

  const handleSaveChanges = () => {
    setLoading(true)
    // Simulate API call
    setTimeout(() => {
      setLoading(false)
      Alert.alert('Success', 'Profile updated successfully!')
    }, 2000)
  }

  return (
    <View style={styles.container}>
      <MyHeader title="Manage Profile" onBackPress={() => navigation.goBack()} />
      <KeyboardAvoidingView behavior="padding" style={{ flex: 1 }}>
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={styles.contentContainer}>
      <View style={styles.content}>
        {/* <Text style={styles.title}>Edit Profile</Text> */}
        <TouchableOpacity onPress={handlePickImage} style={styles.imageWrapper}>
            <Image source={profileImage ? { uri: profileImage } : null} style={styles.profileImage} iconName="account" iconSize={7} />
            <View style={styles.imageOverlay}>
              <MaterialCommunityIcons name="camera" color={Colors.white} size={responsiveFontSize(2.5)} />
            </View>
        </TouchableOpacity>

        <InputBox
          label="Full Name"
          value={name}
          onChangeText={setName}
          leftIcon="account-outline"
        />

        <InputBox
          label="Email Address"
          value={email}
          onChangeText={setEmail}
          leftIcon="email-outline"
          keyboardType="email-address"
          disabled // Email is typically not editable
        />

        <InputBox
          label="Mobile Number"
          value={mobile}
          onChangeText={setMobile}
          leftIcon="phone-outline"
          keyboardType="phone-pad"
        />

        <InputBox
          label="Date of Birth"
          value={dob}
          onChangeText={setDob}
          leftIcon="calendar-outline"
          placeholder="DD/MM/YYYY"
        />

        <CustomDropdown
          label="Gender"
          data={genderData}
          value={gender}
          onChange={setGender}
          leftIcon="gender-male-female"
          search={false}
        />

        {/* Professional Information Section */}
        <Text style={styles.sectionTitle}>Professional Information</Text>

        <CustomDropdown
          label="Qualification"
          data={qualificationData}
          value={qualification}
          onChange={setQualification}
          leftIcon="school-outline"
          placeholder="Select your qualification"
          search={true}
        />

        <DocumentUploadBox
          label="Certification (PDF/JPG)"
          document={certificationDocument}
          onPress={() => handlePickDocument('certification')}
          icon="certificate-outline"
        />

        <CustomDropdown
          label="Total Experience"
          data={experienceData}
          value={totalExperience}
          onChange={setTotalExperience}
          leftIcon="briefcase-outline"
          placeholder="Select experience range"
          search={false}
        />

        <InputBox
          label="Special Skills"
          value={specialSkills}
          onChangeText={setSpecialSkills}
          leftIcon="star-outline"
          placeholder="e.g., pediatric phlebotomy, geriatric care"
          multiline
        />

        {/* Identification & Verification Section */}
        <Text style={styles.sectionTitle}>Identification & Verification</Text>

        <InputBox
          label="Aadhar Card Number"
          value={aadharNumber}
          onChangeText={setAadharNumber}
          leftIcon="card-account-details-outline"
          placeholder="Enter 12-digit Aadhar number"
          keyboardType="numeric"
          maxLength={12}
        />

        <DocumentUploadBox
          label="Upload Aadhar Copy (PDF/JPG)"
          document={aadharDocument}
          onPress={() => handlePickDocument('aadhar')}
          icon="card-account-details"
        />

        <InputBox
          label="PAN Card Number"
          value={panNumber}
          onChangeText={setPanNumber}
          leftIcon="credit-card-outline"
          placeholder="Enter PAN number"
          autoCapitalize="characters"
          maxLength={10}
        />

        <DocumentUploadBox
          label="Upload PAN Copy (PDF/JPG)"
          document={panDocument}
          onPress={() => handlePickDocument('pan')}
          icon="credit-card"
        />

        <CustumBtn
          title="Save Changes"
          onPress={handleSaveChanges}
          loading={loading}
          disabled={loading}
        />
      </View>
    </ScrollView>
    </KeyboardAvoidingView>  
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  contentContainer: {
    flexGrow: 1,
    // justifyContent: 'center',
  },
  content: {
    padding: responsiveWidth(5),
    gap:responsiveHeight(2)
  },
  title: {
    fontSize: responsiveFontSize(3),
    fontWeight: 'bold',
    marginBottom: responsiveHeight(4),
    textAlign: 'center',
    color: Colors.textPrimary,
  },
  imageWrapper: {
    alignSelf: 'center',
    marginBottom: responsiveHeight(2),
    position: 'relative',
  },
  profileImage: {
    width: responsiveWidth(30),
    height: responsiveWidth(30),
    borderRadius: responsiveWidth(15),
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary,
    borderRadius: 20,
    padding: 6,
    borderWidth: 2,
    borderColor: Colors.white,
  },
  sectionTitle: {
    fontSize: responsiveFontSize(2.2),
    fontWeight: 'bold',
    color: Colors.primary,
    marginTop: responsiveHeight(2),
    marginBottom: responsiveHeight(1),
  },
  documentUploadBox: {
    borderWidth: 1,
    borderColor: Colors.gray,
    borderRadius: 8,
    backgroundColor: Colors.surface,
    marginBottom: responsiveHeight(1),
  },
  documentUploadContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: responsiveWidth(4),
    gap: responsiveWidth(3),
  },
  documentUploadText: {
    flex: 1,
  },
  documentUploadLabel: {
    fontSize: responsiveFontSize(1.6),
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  documentUploadStatus: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.textSecondary,
  },
})

export default EditProfile