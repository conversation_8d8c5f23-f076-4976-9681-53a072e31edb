import { View, Text, StyleSheet, ScrollView, Alert, KeyboardAvoidingView, Image, TouchableOpacity } from 'react-native'
import React, { useState } from 'react'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import InputBox from '../../../components/InputBox'
import Colors from '../../../styles/Colors'
import CustomDropdown from '../../../components/CustomDropdown'
import CustumBtn from '../../../components/CustumBtn'
import MyHeader from '../../../components/MyHeader'

const EditProfile = ({navigation}) => {
  const [profileImage, setProfileImage] = useState(null);
  const [name, setName] = useState('<PERSON><PERSON>')
  const [email, setEmail] = useState('<EMAIL>')
  const [mobile, setMobile] = useState('1234567890')
  const [dob, setDob] = useState('01/01/1990') // This can be integrated with a DatePicker
  const [gender, setGender] = useState(null)
  const [bloodGroup, setBloodGroup] = useState('A+')
  const [state, setState] = useState('')
  const [city, setCity] = useState('')
  const [currentAddress, setCurrentAddress] = useState('')
  const [permanentAddress, setPermanentAddress] = useState('')
  const [loading, setLoading] = useState(false)

  const genderData = [
    { label: 'Male', value: 'male' },
    { label: 'Female', value: 'female' },
    { label: 'Other', value: 'other' },
  ];

  const handlePickImage = async () => {

  }

  const handleSaveChanges = () => {
    setLoading(true)
    // Simulate API call
    setTimeout(() => {
      setLoading(false)
      Alert.alert('Success', 'Profile updated successfully!')
    }, 2000)
  }

  return (
    <View style={styles.container}>
      <MyHeader title="Manage Profile" onBackPress={() => navigation.goBack()} />
      <KeyboardAvoidingView behavior="padding" style={{ flex: 1 }}>
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={styles.contentContainer}>
      <View style={styles.content}>
        {/* <Text style={styles.title}>Edit Profile</Text> */}
        <TouchableOpacity onPress={handlePickImage} style={styles.imageWrapper}>
            <Image source={profileImage ? { uri: profileImage } : null} style={styles.profileImage} iconName="account" iconSize={7} />
            <View style={styles.imageOverlay}>
              <MaterialCommunityIcons name="camera" color={Colors.white} size={responsiveFontSize(2.5)} />
            </View>
        </TouchableOpacity>

        <InputBox
          label="Full Name"
          value={name}
          onChangeText={setName}
          leftIcon="account-outline"
        />

        <InputBox
          label="Email Address"
          value={email}
          onChangeText={setEmail}
          leftIcon="email-outline"
          keyboardType="email-address"
          disabled // Email is typically not editable
        />

        <InputBox
          label="Mobile Number"
          value={mobile}
          onChangeText={setMobile}
          leftIcon="phone-outline"
          keyboardType="phone-pad"
        />

        <InputBox
          label="Date of Birth"
          value={dob}
          onChangeText={setDob}
          leftIcon="calendar-outline"
          placeholder="DD/MM/YYYY"
        />

        <CustomDropdown
          label="Gender"
          data={genderData}
          value={gender}
          onChange={setGender}
          leftIcon="gender-male-female"
          search={false}
        />
        
        <CustumBtn
          title="Save Changes"
          onPress={handleSaveChanges}
          loading={loading}
          disabled={loading}
        />
      </View>
    </ScrollView>
    </KeyboardAvoidingView>  
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  contentContainer: {
    flexGrow: 1,
    // justifyContent: 'center',
  },
  content: {
    padding: responsiveWidth(5),
    gap:responsiveHeight(2)
  },
  title: {
    fontSize: responsiveFontSize(3),
    fontWeight: 'bold',
    marginBottom: responsiveHeight(4),
    textAlign: 'center',
    color: Colors.textPrimary,
  },
  imageWrapper: {
    alignSelf: 'center',
    marginBottom: responsiveHeight(2),
    position: 'relative',
  },
  profileImage: {
    width: responsiveWidth(30),
    height: responsiveWidth(30),
    borderRadius: responsiveWidth(15),
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary,
    borderRadius: 20,
    padding: 6,
    borderWidth: 2,
    borderColor: Colors.white,
  },
})

export default EditProfile