import { View, Text, StyleSheet, ScrollView, Alert, KeyboardAvoidingView, Image, TouchableOpacity, ActivityIndicator, ToastAndroid, PermissionsAndroid } from 'react-native'
import React, { useEffect, useState } from 'react'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import InputBox from '../../../components/InputBox'
import Colors from '../../../styles/Colors'
import CustomDropdown from '../../../components/CustomDropdown'
import CustumBtn from '../../../components/CustumBtn'
import MyHeader from '../../../components/MyHeader'
import PickImageComponent from '../../../components/PickImageComponent'

const EditProfile = ({navigation}) => {
  const [profileImage, setProfileImage] = useState(null);
  const [name, setName] = useState('<PERSON><PERSON>')
  const [email, setEmail] = useState('<EMAIL>')
  const [mobile, setMobile] = useState('1234567890')
  const [dob, setDob] = useState('01/01/1990') // This can be integrated with a DatePicker
  const [gender, setGender] = useState('Male')
  const [bloodGroup, setBloodGroup] = useState('A+')
  const [state, setState] = useState('Chhattisgarh')
  const [city, setCity] = useState('Raipur')
  const [currentAddress, setCurrentAddress] = useState('Sundar Nagar, Raipur, Chhattisgarh 492001')
  const [permanentAddress, setPermanentAddress] = useState('Sundar Nagar, Raipur, Chhattisgarh 492001')
  const [workAreaState, setWorkAreaState] = useState('')
  const [workAreaCity, setWorkAreaCity] = useState('')
  const [workAreaPinCode, setWorkAreaPinCode] = useState('')
  const [load, setLoad] = useState(false)

  const genderData = [
    { label: 'Male', value: 'Male' },
    { label: 'Female', value: 'female' },
    { label: 'Other', value: 'other' },
  ];

  const bloodGroupOptions = [
    { label: 'A+', value: 'A+' },
    { label: 'A-', value: 'A-' },
    { label: 'B+', value: 'B+' },
    { label: 'B-', value: 'B-' },
    { label: 'O+', value: 'O+' },
    { label: 'O-', value: 'O-' },
    { label: 'AB+', value: 'AB+' },
    { label: 'AB-', value: 'AB-' },
  ]

  const stateOptions = [
    { label: 'Chhattisgarh', value: 'Chhattisgarh' },
    { label: 'Gujarat', value: 'gujarat' },
    { label: 'Delhi', value: 'delhi' },
    // Add more states as needed
  ]

  const cityOptions = [
    { label: 'Raipur', value: 'Raipur' },
    { label: 'Pune', value: 'pune' },
    { label: 'Ahmedabad', value: 'ahmedabad' },
    { label: 'Delhi', value: 'delhi' },
    // Add more cities as needed
  ]

  const handlePickImage = async (documentType) => {
    try {
      const image = await PickImageComponent();
      if (image) {
        switch (documentType) {
          case 'profile':
            setProfileImage(image.uri);
            break;
        }
      }
    } catch (error) {
      showToast('Failed to pick image');
    }
  }


  const showToast = (msg) => {
    ToastAndroid.showWithGravity(msg, ToastAndroid.LONG, ToastAndroid.CENTER);
  };


  const handleSaveChanges = () => {
    setLoad(true)
    // Simulate API call
    setTimeout(() => {
      setLoad(false)
      Alert.alert('Success', 'Profile updated successfully!')
    }, 2000)
  }

  return (
    <View style={styles.container}>
      <MyHeader title="Edit Profile" onBackPress={() => navigation.goBack()} />
      <KeyboardAvoidingView behavior="padding" style={{ flex: 1 }}>
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={styles.contentContainer}>
      <View style={styles.content}>
        {/* <Text style={styles.title}>Edit Profile</Text> */}
        <TouchableOpacity onPress={() => handlePickImage('profile')} style={styles.imageWrapper}>
            <Image source={profileImage ? { uri: profileImage } : null} style={styles.profileImage} iconName="account" iconSize={7} />
            <View style={styles.imageOverlay}>
              <MaterialCommunityIcons name="camera" color={Colors.white} size={responsiveFontSize(2.5)} />
            </View>
        </TouchableOpacity>

        <InputBox
          label="Full Name"
          value={name}
          onChangeText={setName}
          leftIcon="account-outline"
        />

        <InputBox
          label="Email Address"
          value={email}
          onChangeText={setEmail}
          leftIcon="email-outline"
          keyboardType="email-address"
          disabled // Email is typically not editable
        />

        <InputBox
          label="Mobile Number"
          value={mobile}
          onChangeText={setMobile}
          leftIcon="phone-outline"
          keyboardType="phone-pad"
        />

        <InputBox
          label="Date of Birth"
          value={dob}
          onChangeText={setDob}
          leftIcon="calendar-outline"
          placeholder="DD/MM/YYYY"
        />

        <View style={styles.fieldCmn}>
          <View style={styles.fieldLeft}>
            <CustomDropdown
              label="Gender"
              data={genderData}
              value={gender}
              onChange={setGender}
              leftIcon="gender-male-female"
              search={false}
            />
          </View>
          <View style={styles.fieldRight}>
            <CustomDropdown
              label="Blood Group"
              data={bloodGroupOptions}
              value={bloodGroup}
              onChange={setBloodGroup}
              leftIcon="water"
              search={false}
            />
          </View>
        </View>

        <View style={styles.fieldCmn}>
          <View style={styles.fieldLeft}>
            <CustomDropdown
              label="State"
              data={stateOptions}
              value={state}
              onChange={setState}
              placeholder="Select State"
              leftIcon="map"
            />
          </View>
          <View style={styles.fieldRight}>
            <CustomDropdown
              label="City"
              data={cityOptions}
              value={city}
              onChange={setCity}
              placeholder="Select City"
              leftIcon="city"
            />
          </View>
        </View>
        
        <InputBox
          label="Current Address"
          value={currentAddress}
          onChangeText={setCurrentAddress}
          leftIcon="map-marker-outline"
          placeholder="Enter current address"
          multiline
        />

        <InputBox
          label="Permanent Address"
          value={permanentAddress}
          onChangeText={setPermanentAddress}
          leftIcon="home-outline"
          placeholder="Enter permanent address"
          multiline
        />

        <View style={styles.fieldCmn}>
            <View style={[styles.fieldLeft,{flex:2.1}]}>
              <CustomDropdown label="Work State" data={stateOptions} value={workAreaState} onChange={setWorkAreaState} placeholder="Work State" leftIcon="map" />
            </View>
            <View style={[styles.fieldRight,{flex:2.1}]}>
              <CustomDropdown  label="Work City" data={cityOptions} value={workAreaCity} onChange={setWorkAreaCity} placeholder="Work City" leftIcon="city" />
            </View>
        </View>

        <InputBox
          label="Work Area PinCode"
          value={workAreaPinCode}
          onChangeText={setWorkAreaPinCode}
          leftIcon="map-marker-radius"
          placeholder="Pincode"
        />


        <CustumBtn
          title="Save Changes"
          onPress={handleSaveChanges}
          loading={load}
          disabled={load}
        />
      </View>
    </ScrollView>
    </KeyboardAvoidingView>  
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  contentContainer: {
    flexGrow: 1,
    // justifyContent: 'center',
  },
  content: {
    padding: responsiveWidth(5),
    gap:responsiveHeight(2)
  },
  title: {
    fontSize: responsiveFontSize(3),
    fontWeight: 'bold',
    marginBottom: responsiveHeight(4),
    textAlign: 'center',
    color: Colors.textPrimary,
  },
  imageWrapper: {
    alignSelf: 'center',
    marginBottom: responsiveHeight(2),
    position: 'relative',
  },
  profileImage: {
    width: responsiveWidth(30),
    height: responsiveWidth(30),
    borderRadius: responsiveWidth(15),
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary,
    borderRadius: 20,
    padding: 6,
    borderWidth: 2,
    borderColor: Colors.white,
  },
  sectionTitle: {
    fontSize: responsiveFontSize(2.2),
    fontWeight: 'bold',
    color: Colors.primary,
    marginTop: responsiveHeight(2),
    marginBottom: responsiveHeight(1),
  },
  documentUploadBox: {
    borderWidth: 1,
    borderColor: Colors.gray,
    borderRadius: 8,
    backgroundColor: Colors.surface,
    marginBottom: responsiveHeight(1),
  },
  documentUploadContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: responsiveWidth(4),
    gap: responsiveWidth(3),
  },
  documentUploadText: {
    flex: 1,
  },
  documentUploadLabel: {
    fontSize: responsiveFontSize(1.6),
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  documentUploadStatus: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.textSecondary,
  },
  subSectionTitle: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: '600',
    color: Colors.textPrimary,
    marginTop: responsiveHeight(1),
    marginBottom: responsiveHeight(0.5),
  },
  checkboxContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: responsiveHeight(1),
  },
  checkboxItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginBottom: responsiveHeight(0.5),
  },
  checkboxLabel: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.textPrimary,
    marginLeft: responsiveWidth(1),
  },
  switchContainer: {
    marginBottom: responsiveHeight(1),
  },
  switchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: responsiveWidth(4),
    backgroundColor: Colors.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.gray,
    gap: responsiveWidth(3),
  },
  switchLabel: {
    flex: 1,
    fontSize: responsiveFontSize(1.6),
    color: Colors.textPrimary,
  },
  fieldCmn:{
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: responsiveWidth(2),
    marginTop: responsiveHeight(0.5)
  },
  fieldLeft:{
    flex:2,
  },
  fieldRight:{
    flex:2,
  },
})

export default EditProfile