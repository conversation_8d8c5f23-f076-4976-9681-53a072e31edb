import { View, Text, StyleSheet, ScrollView, Alert, KeyboardAvoidingView, ToastAndroid, ActivityIndicator, TouchableOpacity, PermissionsAndroid } from 'react-native'
import React, { useEffect, useState } from 'react'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import InputBox from '../../../components/InputBox'
import Colors from '../../../styles/Colors'
import CustomDropdown from '../../../components/CustomDropdown'
import CustumBtn from '../../../components/CustumBtn'
import MyHeader from '../../../components/MyHeader'
import {  Checkbox, Switch } from 'react-native-paper'
import DeviceInfo from 'react-native-device-info';
import Geolocation from '@react-native-community/geolocation';

const DeviceAvailabilityInfo = ({navigation}) => {
  // Device & Availability Info
  const [preferredWorkArea, setPreferredWorkArea] = useState('')
  const [availableDays, setAvailableDays] = useState([])
  const [timeSlots, setTimeSlots] = useState('')
  const [location, setLocation] = useState(null)
  const [gpsAccess, setGpsAccess] = useState(false)
  const [deviceInfo, setDeviceInfo] = useState('')
  const [loading, setLoading] = useState(false)
  const [load, setLoad] = useState(false)

  const daysData = [
    { label: 'Monday', value: 'monday' },
    { label: 'Tuesday', value: 'tuesday' },
    { label: 'Wednesday', value: 'wednesday' },
    { label: 'Thursday', value: 'thursday' },
    { label: 'Friday', value: 'friday' },
    { label: 'Saturday', value: 'saturday' },
    { label: 'Sunday', value: 'sunday' },
  ];

  const timeSlotData = [
    { label: 'Morning (6 AM - 12 PM)', value: 'morning' },
    { label: 'Afternoon (12 PM - 6 PM)', value: 'afternoon' },
    { label: 'Evening (6 PM - 10 PM)', value: 'evening' },
    { label: 'Full Day (6 AM - 10 PM)', value: 'fullday' },
    { label: 'Flexible', value: 'flexible' },
  ];

  const handleDaySelection = (day) => {
    setAvailableDays(prev => {
      if (prev.includes(day)) {
        return prev.filter(d => d !== day);
      } else {
        return [...prev, day];
      }
    });
  };

  const handleSaveChanges = () => {
    setLoad(true)
    // Simulate API call
    setTimeout(() => {
      setLoad(false)
      Alert.alert('Success', 'Device & availability information updated successfully!')
    }, 2000)
  }

   // Location permission
  const requestLocationPermission = async () => {
    console.log('Requesting location permission...');
    try {
      // Ask both fine and coarse location
      const granted = await PermissionsAndroid.requestMultiple([
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
        PermissionsAndroid.PERMISSIONS.ACCESS_BACKGROUND_LOCATION,
      ]);

      const fine = granted[PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION] === PermissionsAndroid.RESULTS.GRANTED;
      const coarse = granted[PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION] === PermissionsAndroid.RESULTS.GRANTED;

      if (fine || coarse) {
        console.log("permission granted")
        getCurrentPosition();
        
      } else {
        showToast('Please enable location permission in settings.');
        setLoading(false);
      }
    } catch (err) {
      console.warn(err);
      showToast('Permission error');
    }
  }

  const getCurrentPosition = () => {
    console.log('Fetching current location...');
    setLoading(true);

    Geolocation.getCurrentPosition(
      (position) => {
        console.log('Location:', position.coords);
        setLocation(position.coords);
        setLoading(false);
      },
      (error) => {
        console.log('GetCurrentPosition Error:', error);
        console.log(`❌ GetCurrentPosition Error [code ${error.code}]:`, error.message);

        if (error.code === 1) {
          showToast('Permission denied.');
        } else if (error.code === 2) {
          showToast('Location unavailable. Turn on GPS.');
        } else if (error.code === 3) {
          // showToast('Location timeout. Try again.');
        } else {
          // showToast(error.message || 'Unknown location error');
        }
        useWatchPositionFallback()
        // setLoading(false);
      },
      {
        enableHighAccuracy: false,
        timeout: 20000,
        maximumAge: 10000,
        forceRequestLocation: true,
        showLocationDialog: true,
      }
    );
  };

  // fallback using watchPosition
  const useWatchPositionFallback = () => {
    const watchId = Geolocation.watchPosition(
      (position) => {
        console.log('✅ Fallback position:', position.coords);
        setLocation(position.coords);
        Geolocation.clearWatch(watchId);
        // navigation.reset({ index: 0, routes: [{ name: 'Dashboard' }] })
        setLoading(false);
      },
      (error) => {
        console.log('❌ Fallback error:', error);
        showToast('Unable to fetch location');
        setLoading(false);
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 0,
        interval: 5000,
        fastestInterval: 2000,
      }
    );
  };

  useEffect(() => {
    const fetchVersion = async () => {
            try {
                const version = await DeviceInfo.getVersion();
                setDeviceInfo(version);
                console.log('App Version:', version);
            } catch (error) {
                console.log('Error fetching version:', error);
            }
    };
    fetchVersion();
    // getToken();
  }, []);

  const showToast = (msg) => {
    ToastAndroid.showWithGravity(msg, ToastAndroid.LONG, ToastAndroid.CENTER);
  };

  return (
    <View style={styles.container}>
      <MyHeader title="Device & Availability Info" onBackPress={() => navigation.goBack()} />
      <KeyboardAvoidingView behavior="padding" style={{ flex: 1 }}>
        <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={styles.contentContainer}>
          <View style={styles.content}>

            {/* <InputBox
              label="Preferred Work Area"
              value={preferredWorkArea}
              onChangeText={setPreferredWorkArea}
              leftIcon="map-marker-outline"
              placeholder="City/Zone/Pincode"
            /> */}

            <Text style={styles.subSectionTitle}>Available Days</Text>
            <View style={styles.checkboxContainer}>
              {daysData.map((day) => (
                <View key={day.value} style={styles.checkboxItem}>
                  <Checkbox
                    status={availableDays.includes(day.value) ? 'checked' : 'unchecked'}
                    onPress={() => handleDaySelection(day.value)}
                    color={Colors.primary}
                  />
                  <Text style={styles.checkboxLabel}>{day.label}</Text>
                </View>
              ))}
            </View>

            <CustomDropdown
              label="Time Slots"
              data={timeSlotData}
              value={timeSlots}
              onChange={setTimeSlots}
              leftIcon="clock-outline"
              placeholder="Select preferred time slots"
              search={false}
            />

            {/* <View style={styles.fieldCmn}>
                <View style={styles.fieldLeft}>
                  <InputBox label="Your Location" icon="map-marker" keyboardType={'number-pad'} editable={false} value={location ? `${location.latitude}, ${location.longitude}` : ''} onChangeText={(text) => console.log(text)} />
                </View>
                {loading ? (
                    <View style={{ flex: 1, }}>
                      <ActivityIndicator size={responsiveFontSize(2.2)} color={Colors.primary} />
                    </View>
                  ) : 
                    <TouchableOpacity onPress={()=>requestLocationPermission()} style={{flex:1.2 ,flexDirection:'row', alignItems:'center', gap:responsiveWidth(2),}}>
                      <MaterialCommunityIcons name="crosshairs-gps" size={responsiveFontSize(2.2)} color={Colors.primary} />
                      <Text style={{fontSize:responsiveFontSize(1.6), color:Colors.primary}}>Get Location</Text>
                    </TouchableOpacity>
                }
            </View> */}
            
            <CustumBtn
              title="Save Changes"
              onPress={handleSaveChanges}
              loading={load}
              disabled={load}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>  
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  contentContainer: {
    flexGrow: 1,
  },
  content: {
    padding: responsiveWidth(5),
    gap: responsiveHeight(2)
  },
  subtitle: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: responsiveHeight(1),
  },
  subSectionTitle: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: '600',
    color: Colors.textPrimary,
    marginTop: responsiveHeight(1),
    marginBottom: responsiveHeight(0.5),
  },
  checkboxContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: responsiveHeight(1),
  },
  checkboxItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginBottom: responsiveHeight(0.5),
  },
  checkboxLabel: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.textPrimary,
    marginLeft: responsiveWidth(1),
  },
  switchContainer: {
    marginBottom: responsiveHeight(1),
  },
  switchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: responsiveWidth(4),
    backgroundColor: Colors.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.gray,
    gap: responsiveWidth(3),
  },
  switchLabel: {
    flex: 1,
    fontSize: responsiveFontSize(1.6),
    color: Colors.textPrimary,
  },
  fieldCmn:{
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: responsiveWidth(2),
    marginTop: responsiveHeight(0.5)
  },
  fieldLeft:{
    flex:2,
  },
  fieldRight:{
    flex:2,
  },
})

export default DeviceAvailabilityInfo
