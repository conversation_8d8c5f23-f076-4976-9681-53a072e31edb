import { View, Text, TouchableOpacity, Image } from 'react-native'
import React, { useState } from 'react'
import InputBox from '../../components/InputBox'
import CustumBtn from '../../components/CustumBtn'
import { responsiveWidth, responsiveHeight } from 'react-native-responsive-dimensions'
import { useNavigation } from '@react-navigation/native'
import Colors from '../../styles/Colors'
import BottomLink from '../../components/BottomLink'

const Login = ({navigation}) => {
  const [mobile, setMobile] = useState('')
  const [password, setPassword] = useState('')

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: Colors.surface,
        justifyContent: 'center',
        paddingHorizontal: responsiveWidth(5),
        paddingVertical: responsiveHeight(2),
      }}
    >
      <Image
        source={require('../../accets/images/fullLogoWithoutBg.png')}
        style={{
          width: responsiveWidth(40),
          height: responsiveHeight(20),
          alignSelf: 'center',
          marginBottom: responsiveHeight(3),
          resizeMode: 'contain',
        }}
      />
      <View style={{gap: responsiveHeight(2)}}>
        <InputBox
          label="Mobile"
          placeholder="Enter Mobile Number"
          value={mobile}
          onChangeText={setMobile}
          keyboardType="phone-pad"
          leftIcon="phone"
        />
        <InputBox
          label="Password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          leftIcon="lock"
          placeholder="Enter Password"
        />
      </View>

      <View style={{ alignSelf: 'flex-end', marginVertical: responsiveHeight(1) }}>
        <BottomLink subText={'Forgot Password?'} subTextColor={Colors.green} navigation={navigation} onPress={()=> navigation.navigate('ForgotPassword')} />
      </View>

      <CustumBtn title="Login" onPress={() => navigation.navigate('Main')} />

      <View style={{ alignSelf: 'center', marginTop: responsiveHeight(2) }} >
        <BottomLink text={'Don\'t have an account? '} subText={'Sign Up'} subTextColor={Colors.green} navigation={navigation} onPress={()=>  navigation.navigate('SignUp')} />
      </View>
    </View>
  )
}

export default Login