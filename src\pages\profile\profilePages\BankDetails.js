import { View, Text, StyleSheet, ScrollView, Alert, KeyboardAvoidingView, TouchableOpacity } from 'react-native'
import React, { useState } from 'react'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import InputBox from '../../../components/InputBox'
import Colors from '../../../styles/Colors'
import CustumBtn from '../../../components/CustumBtn'
import MyHeader from '../../../components/MyHeader'
import PickImageComponent from '../../../components/PickImageComponent';

const BankDetails = ({navigation}) => {
  // Bank Details
  const [bankName, setBankName] = useState('')
  const [accountHolderName, setAccountHolderName] = useState('')
  const [accountNumber, setAccountNumber] = useState('')
  const [ifscCode, setIfscCode] = useState('')
  const [bankDocument, setBankDocument] = useState(null)
  const [loading, setLoading] = useState(false)

  const handlePickDocument = async () => {
    try {
      const document = await PickImageComponent();
      if (document) {
        setBankDocument(document);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick document');
    }
  }

  const DocumentUploadBox = ({ label, document, onPress, icon = "file-document-outline" }) => (
    <TouchableOpacity onPress={onPress} style={styles.documentUploadBox}>
      <View style={styles.documentUploadContent}>
        <MaterialCommunityIcons name={icon} size={responsiveFontSize(2.5)} color={Colors.primary} />
        <View style={styles.documentUploadText}>
          <Text style={styles.documentUploadLabel}>{label}</Text>
          <Text style={styles.documentUploadStatus}>
            {document ? document.fileName || 'Document Selected' : 'Tap to upload'}
          </Text>
        </View>
        <MaterialCommunityIcons 
          name={document ? "check-circle" : "upload"} 
          size={responsiveFontSize(2)} 
          color={document ? Colors.green : Colors.gray} 
        />
      </View>
    </TouchableOpacity>
  );

  const handleSaveChanges = () => {
    // Basic validation
    if (!bankName.trim()) {
      Alert.alert('Error', 'Please enter bank name');
      return;
    }
    if (!accountHolderName.trim()) {
      Alert.alert('Error', 'Please enter account holder name');
      return;
    }
    if (!accountNumber.trim()) {
      Alert.alert('Error', 'Please enter account number');
      return;
    }
    if (!ifscCode.trim() || ifscCode.length !== 11) {
      Alert.alert('Error', 'Please enter a valid 11-character IFSC code');
      return;
    }

    setLoading(true)
    // Simulate API call
    setTimeout(() => {
      setLoading(false)
      Alert.alert('Success', 'Bank details updated successfully!')
    }, 2000)
  }

  return (
    <View style={styles.container}>
      <MyHeader title="Bank Details" onBackPress={() => navigation.goBack()} />
      <KeyboardAvoidingView behavior="padding" style={{ flex: 1 }}>
        <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={styles.contentContainer}>
          <View style={styles.content}>
            
            {/* <Text style={styles.subtitle}>For payout/commission</Text> */}

            <InputBox
              label="Bank Name"
              value={bankName}
              onChangeText={setBankName}
              leftIcon="bank-outline"
              placeholder="Enter bank name"
            />

            <InputBox
              label="Account Holder Name"
              value={accountHolderName}
              onChangeText={setAccountHolderName}
              leftIcon="account-outline"
              placeholder="Enter account holder name"
            />

            <InputBox
              label="Account Number"
              value={accountNumber}
              onChangeText={setAccountNumber}
              leftIcon="credit-card-outline"
              placeholder="Enter account number"
              keyboardType="numeric"
            />

            <InputBox
              label="IFSC Code"
              value={ifscCode}
              onChangeText={setIfscCode}
              leftIcon="bank-transfer"
              placeholder="Enter IFSC code"
              autoCapitalize="characters"
              maxLength={11}
            />

            <DocumentUploadBox
              label="Cancelled Cheque or Passbook Photo"
              document={bankDocument}
              onPress={handlePickDocument}
              icon="file-document-outline"
            />
            
            <CustumBtn
              title="Save Changes"
              onPress={handleSaveChanges}
              loading={loading}
              disabled={loading}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>  
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  contentContainer: {
    flexGrow: 1,
  },
  content: {
    padding: responsiveWidth(5),
    gap: responsiveHeight(2)
  },
  subtitle: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: responsiveHeight(1),
  },
  documentUploadBox: {
    borderWidth: 1,
    borderColor: Colors.gray,
    borderRadius: 8,
    backgroundColor: Colors.surface,
    marginBottom: responsiveHeight(1),
  },
  documentUploadContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: responsiveWidth(4),
    gap: responsiveWidth(3),
  },
  documentUploadText: {
    flex: 1,
  },
  documentUploadLabel: {
    fontSize: responsiveFontSize(1.6),
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  documentUploadStatus: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.textSecondary,
  },
})

export default BankDetails
