import React, { useState } from 'react'
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Dimensions
} from 'react-native'
import Colors from '../../styles/Colors'
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth
} from 'react-native-responsive-dimensions'
import Icon from 'react-native-vector-icons/MaterialCommunityIcons'
import { useNavigation } from '@react-navigation/native'
import LinearGradient from 'react-native-linear-gradient'

const { width } = Dimensions.get('window')

const Profile = () => {
  const navigation = useNavigation()
  const [user] = useState({
    name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    joined: 'Joined July 2024'
  })

  const menuItems = [
    // { icon: 'account-edit-outline', text: 'Edit Profile', screen: 'EditProfile', color: '#4CAF50' },
    { icon: 'account-cog-outline', text: 'Profile Management', screen: 'ProfileManagement', color: '#2196F3' },
    { icon: 'wallet-outline', text: 'Balance Transaction', screen: 'Transaction', color: '#2196F3' },
    { icon: 'lock-outline', text: 'Change Password', screen: 'ChangePassword', color: '#FF9800' },
    // { icon: 'bell-outline', text: 'Notifications', screen: 'Notifications', color: '#9C27B0' },
    { icon: 'help-circle-outline', text: 'Help & Support', screen: 'Help', color: '#607D8B' },
    { icon: 'information-outline', text: 'About Us', screen: 'About', color: '#795548' }
  ]

  const handleLogout = () => {
    navigation.reset({
      index: 0,
      routes: [{ name: 'Login' }]
    })
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header Gradient */}
        <LinearGradient
          colors={[Colors.primary, '#0088CC']}
          style={styles.headerGradient}
        >
          <View style={styles.profileCard}>
            <View style={styles.avatarWrapper}>
              <LinearGradient
                colors={['#ffffff', '#f0f0f0']}
                style={styles.avatarGradient}
              >
                <Icon name="account" size={responsiveFontSize(8)} color={Colors.primary} />
              </LinearGradient>
            </View>
            <Text style={styles.name}>{user.name}</Text>
            <Text style={styles.email}>{user.email}</Text>
            <View style={styles.joinedBadge}>
              <Text style={styles.joined}>{user.joined}</Text>
            </View>
          </View>
        </LinearGradient>

        {/* Settings List */}
        <View style={styles.menuContainer}>
          <Text style={styles.sectionTitle}>Account Settings</Text>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.menuItem, index === menuItems.length - 1 && styles.lastMenuItem]}
              onPress={() => navigation.navigate(item.screen)}
              activeOpacity={0.7}
            >
              <View style={styles.menuLeft}>
                <View style={[styles.iconWrapper, { backgroundColor: `${item.color}15` }]}>
                  <Icon name={item.icon} size={responsiveFontSize(2.4)} color={item.color} />
                </View>
                <Text style={styles.menuText}>{item.text}</Text>
              </View>
              <Icon name="chevron-right" size={responsiveFontSize(2.2)} color={Colors.textSecondary} />
            </TouchableOpacity>
          ))}
        </View>

        {/* Logout Section */}
        <View style={styles.logoutSection}>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout} activeOpacity={0.8}>
            <Icon name="logout" size={responsiveFontSize(2.2)} color={Colors.error} />
            <Text style={styles.logoutText}>Logout</Text>
          </TouchableOpacity>
          <Text style={styles.version}>App Version 1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

export default Profile

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background
  },
  headerGradient: {
    paddingBottom: responsiveHeight(3),
    borderBottomLeftRadius: responsiveWidth(8),
    borderBottomRightRadius: responsiveWidth(8),
  },
  profileCard: {
    alignItems: 'center',
    paddingVertical: responsiveHeight(4),
    paddingHorizontal: responsiveWidth(4),
  },
  avatarWrapper: {
    marginBottom: responsiveHeight(2),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  avatarGradient: {
    width: responsiveWidth(28),
    height: responsiveWidth(28),
    borderRadius: responsiveWidth(14),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  name: {
    fontSize: responsiveFontSize(2.8),
    fontWeight: 'bold',
    color: Colors.surface,
    marginBottom: responsiveHeight(0.5),
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  email: {
    fontSize: responsiveFontSize(1.8),
    color: 'rgba(255,255,255,0.9)',
    marginBottom: responsiveHeight(1),
  },
  joinedBadge: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: responsiveWidth(3),
    paddingVertical: responsiveHeight(0.5),
    borderRadius: responsiveWidth(4),
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  joined: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.surface,
    fontWeight: '500',
  },
  sectionTitle: {
    fontSize: responsiveFontSize(2.2),
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: responsiveHeight(2),
    marginLeft: responsiveWidth(4),
  },
  menuContainer: {
    marginTop: responsiveHeight(3),
    marginHorizontal: responsiveWidth(4),
    backgroundColor: Colors.surface,
    borderRadius: responsiveWidth(4),
    paddingTop: responsiveHeight(2),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  menuItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: responsiveHeight(2.5),
    paddingHorizontal: responsiveWidth(4),
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  lastMenuItem: {
    borderBottomWidth: 0,
    paddingBottom: responsiveHeight(3),
  },
  menuLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconWrapper: {
    width: responsiveWidth(10),
    height: responsiveWidth(10),
    borderRadius: responsiveWidth(5),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: responsiveWidth(4),
  },
  menuText: {
    fontSize: responsiveFontSize(2),
    color: Colors.textPrimary,
    fontWeight: '500',
  },
  logoutSection: {
    marginTop: responsiveHeight(4),
    paddingHorizontal: responsiveWidth(4),
    alignItems: 'center',
    marginBottom: responsiveHeight(3),
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    paddingVertical: responsiveHeight(2),
    paddingHorizontal: responsiveWidth(8),
    borderRadius: responsiveWidth(8),
    borderWidth: 2,
    borderColor: Colors.error,
    shadowColor: Colors.error,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  logoutText: {
    fontSize: responsiveFontSize(2),
    color: Colors.error,
    fontWeight: 'bold',
    marginLeft: responsiveWidth(2),
  },
  version: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.textSecondary,
    marginTop: responsiveHeight(3),
    textAlign: 'center',
    fontStyle: 'italic',
  }
})
