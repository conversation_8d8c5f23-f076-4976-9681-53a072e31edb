import { View, Text, StyleSheet, Alert } from 'react-native'
import React, { useState } from 'react'
import CustumBtn from '../../../components/CustumBtn';
import { TextInput } from 'react-native-paper';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import InputBox from '../../../components/InputBox';
import Colors from '../../../styles/Colors';
import MyHeader from '../../../components/MyHeader';

const ChangePassword = ({navigation}) => {
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const validate = () => {
    const newErrors = {};
    if (!oldPassword) {
      newErrors.oldPassword = 'Old password is required.';
    }
    if (!newPassword) {
      newErrors.newPassword = 'New password is required.';
    } else if (newPassword.length < 6) {
      newErrors.newPassword = 'Password must be at least 6 characters.';
    }
    if (newPassword !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match.';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChangePassword = () => {
    if (validate()) {
      setLoading(true);
      // Simulate API call
      setTimeout(() => {
        setLoading(false);
        Alert.alert('Success', 'Password changed successfully!');
        // Reset fields
        setOldPassword('');
        setNewPassword('');
        setConfirmPassword('');
        setErrors({});
      }, 2000);
    }
  };

  return (
    <View style={styles.main}>
      <MyHeader title="Change Password" onBackPress={() => navigation.goBack()} />
      <View style={styles.container}>
        {/* <Text style={styles.title}>Change Password</Text> */}
        <InputBox
          label="Old Password"
          value={oldPassword}
          onChangeText={setOldPassword}
          secureTextEntry={!showOldPassword}
          leftIcon="lock-outline"
          right={<TextInput.Icon icon={showOldPassword ? 'eye' : 'eye-off'} size={responsiveFontSize(2.6)} onPress={() => setShowOldPassword(!showOldPassword)} />}
          error={!!errors.oldPassword}
        />

        <InputBox
          label="New Password"
          value={newPassword}
          onChangeText={setNewPassword}
          secureTextEntry={!showNewPassword}
          leftIcon="lock-outline"
          right={<TextInput.Icon icon={showNewPassword ? 'eye' : 'eye-off'} size={responsiveFontSize(2.6)} onPress={() => setShowNewPassword(!showNewPassword)} />}
          error={!!errors.newPassword}
        />

        <InputBox
          label="Confirm New Password"
          value={confirmPassword}
          onChangeText={setConfirmPassword}
          secureTextEntry={!showConfirmPassword}
          leftIcon="lock-outline"
          right={<TextInput.Icon icon={showConfirmPassword ? 'eye' : 'eye-off'} size={responsiveFontSize(2.6)} onPress={() => setShowConfirmPassword(!showConfirmPassword)} />}
          error={!!errors.confirmPassword}
        />

        <CustumBtn
          title="Change Password"
          onPress={handleChangePassword}
          style={styles.button}
          loading={loading}
          disabled={loading}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  main:{
    flex: 1,
    backgroundColor: Colors.background,
  },
  container: {
    padding: responsiveWidth(4),
  },
  title: {
    fontSize: responsiveFontSize(3),
    fontWeight: 'bold',
    marginBottom: responsiveHeight(4),
    textAlign: 'center',
  },
  button: {
    marginTop: responsiveHeight(4),
  },
});

export default ChangePassword