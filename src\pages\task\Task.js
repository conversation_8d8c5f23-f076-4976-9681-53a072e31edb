import { View, Text, StyleSheet, SafeAreaView, FlatList, TouchableOpacity, Linking, Platform, Alert, Pressable } from 'react-native'
import React, { useState, useMemo } from 'react'
import { responsiveHeight, responsiveWidth, responsiveFontSize } from 'react-native-responsive-dimensions'
import Colors from '../../styles/Colors'
import Icon from 'react-native-vector-icons/MaterialCommunityIcons'
import LinearGradient from 'react-native-linear-gradient'
import ConfirmationModal from '../../components/ConfirmationModal'

// Mock data for demonstration purposes
const mockTasks = [
  {
    id: '1',
    patientName: '<PERSON><PERSON>',
    address: 'Sector 1, Bhilai, Durg, Chhattisgarh 490001',
    time: '10:30 AM',
    date: 'Today',
    status: 'pending',
    priority: 'high',
    testDetails: [
        { id: '1', name: 'CBC TEST', SLDCode: 'SLDC126', pre_test_info: 'No special preparation required',  specimen_details: '2 mL Whole Blood (EDTA)', fasting_required: 'inactive',
          speciality: {
            id: 22,
           name: 'Pediatrician', // For children
          },
          containerType: [
            { id: '1', name: '<PERSON> Top(Red)', tubeColor: '#C00000',},
            { id: '2', name: 'SST(Gold)', tubeColor: '#FFC000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
        { id: '2', name: 'VITAMIN D3 TEST', SLDCode: 'VSLD020', pre_test_info: "12 hours fasting required", specimen_details: "5 mL Serum", fasting_required: "active", 
          speciality: {
            id: 101,
            name: 'Internal Medicine', // For adults
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
        { id: '3', name: 'Urine Test', SLDCode: 'USLD014', pre_test_info: 'Collect midstream urine sample', specimen_details: '10 mL Urine', fasting_required: "inactive", 
          speciality: {
            id: 22,
           name: 'Pediatrician', // For children
          },
          containerType: [
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
        
      ],
  },
  {
    id: '2',
    patientName: 'Priya Sharma',
    address: 'Civil Lines, Raipur, Chhattisgarh 492001',
    time: '11:45 AM',
    date: 'Today',
    status: 'pending',
    priority: 'medium',
    testDetails: [
        { id: '1', name: 'CBC TEST', SLDCode: 'SLDC126', pre_test_info: 'No special preparation required',  specimen_details: '2 mL Whole Blood (EDTA)', fasting_required: 'inactive',
          speciality: {
            id: 22,
           name: 'Pediatrician', // For children
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '2', name: 'SST(Gold)', tubeColor: '#FFC000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
        { id: '2', name: 'VITAMIN D3 TEST', SLDCode: 'VSLD020', pre_test_info: "12 hours fasting required", specimen_details: "5 mL Serum", fasting_required: "active", 
          speciality: {
            id: 101,
            name: 'Internal Medicine', // For adults
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
      ],
  },
  {
    id: '3',
    patientName: 'Amit Verma',
    address: 'Telibandha, Raipur, Chhattisgarh 492006',
    time: '01:15 PM',
    date: 'Today',
    status: 'accepted',
    priority: 'low',
    testDetails: [
        { id: '1', name: 'CBC TEST', SLDCode: 'SLDC126', pre_test_info: 'No special preparation required',  specimen_details: '2 mL Whole Blood (EDTA)', fasting_required: 'inactive',
          speciality: {
            id: 22,
           name: 'Pediatrician', // For children
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '2', name: 'SST(Gold)', tubeColor: '#FFC000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
        { id: '2', name: 'VITAMIN D3 TEST', SLDCode: 'VSLD020', pre_test_info: "12 hours fasting required", specimen_details: "5 mL Serum", fasting_required: "active", 
          speciality: {
            id: 101,
            name: 'Internal Medicine', // For adults
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
      ],
  },
  {
    id: '4',
    patientName: 'Sunita Patel',
    address: 'Supela, Bhilai, Durg, Chhattisgarh 490023',
    time: '02:00 PM',
    date: 'Today',
    status: 'accepted',
    priority: 'high',
    testDetails: [
        { id: '1', name: 'CBC TEST', SLDCode: 'SLDC126', pre_test_info: 'No special preparation required',  specimen_details: '2 mL Whole Blood (EDTA)', fasting_required: 'inactive',
          speciality: {
            id: 22,
           name: 'Pediatrician', // For children
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '2', name: 'SST(Gold)', tubeColor: '#FFC000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
        { id: '2', name: 'VITAMIN D3 TEST', SLDCode: 'VSLD020', pre_test_info: "12 hours fasting required", specimen_details: "5 mL Serum", fasting_required: "active", 
          speciality: {
            id: 101,
            name: 'Internal Medicine', // For adults
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
      ],
  },
  {
    id: '5',
    patientName: 'Vikash Singh',
    address: 'Korba Road, Bilaspur, Chhattisgarh 495001',
    time: '09:00 AM',
    date: 'Today',
    status: 'completed',
    priority: 'medium',
    testDetails: [
        { id: '1', name: 'CBC TEST', SLDCode: 'SLDC126', pre_test_info: 'No special preparation required',  specimen_details: '2 mL Whole Blood (EDTA)', fasting_required: 'inactive',
          speciality: {
            id: 22,
           name: 'Pediatrician', // For children
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '2', name: 'SST(Gold)', tubeColor: '#FFC000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
        { id: '2', name: 'VITAMIN D3 TEST', SLDCode: 'VSLD020', pre_test_info: "12 hours fasting required", specimen_details: "5 mL Serum", fasting_required: "active", 
          speciality: {
            id: 101,
            name: 'Internal Medicine', // For adults
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
      ],
  },
  {
    id: '6',
    patientName: 'Kavita Jain',
    address: 'Shankar Nagar, Raipur, Chhattisgarh 492007',
    time: '09:45 AM',
    date: 'Today',
    status: 'completed',
    priority: 'low',
    testDetails: [
        { id: '1', name: 'CBC TEST', SLDCode: 'SLDC126', pre_test_info: 'No special preparation required',  specimen_details: '2 mL Whole Blood (EDTA)', fasting_required: 'inactive',
          speciality: {
            id: 22,
           name: 'Pediatrician', // For children
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '2', name: 'SST(Gold)', tubeColor: '#FFC000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
        { id: '2', name: 'VITAMIN D3 TEST', SLDCode: 'VSLD020', pre_test_info: "12 hours fasting required", specimen_details: "5 mL Serum", fasting_required: "active", 
          speciality: {
            id: 101,
            name: 'Internal Medicine', // For adults
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
      ],
  },
  {
    id: '7',
    patientName: 'Deepak Gupta',
    address: 'Station Road, Durg, Chhattisgarh 491001',
    time: '03:30 PM',
    date: 'Today',
    status: 'cancelled',
    priority: 'medium',
    testDetails: [
        { id: '1', name: 'CBC TEST', SLDCode: 'SLDC126', pre_test_info: 'No special preparation required',  specimen_details: '2 mL Whole Blood (EDTA)', fasting_required: 'inactive',
          speciality: {
            id: 22,
           name: 'Pediatrician', // For children
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '2', name: 'SST(Gold)', tubeColor: '#FFC000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
        { id: '2', name: 'VITAMIN D3 TEST', SLDCode: 'VSLD020', pre_test_info: "12 hours fasting required", specimen_details: "5 mL Serum", fasting_required: "active", 
          speciality: {
            id: 101,
            name: 'Internal Medicine', // For adults
          },
          containerType: [
            { id: '1', name: 'Red Top(Red)', tubeColor: '#C00000',},
            { id: '5', name: 'SODIUM CITERATE(Light Blue)', tubeColor: '#558ED5',},
          ],
        },
      ],
  },
]

const Task = () => {
  const [tasks, setTasks] = useState(mockTasks)
  const [activeTab, setActiveTab] = useState('Accepted')
  const [expandedCards, setExpandedCards] = useState({})
  const [confirmationModal, setConfirmationModal] = useState({
    visible: false,
    type: '',
    taskId: null,
    taskName: ''
  })

  const tabs = ['Accepted', 'Completed', 'Cancelled']

  const showConfirmation = (type, taskId, taskName) => {
    setConfirmationModal({
      visible: true,
      type,
      taskId,
      taskName
    })
  }

  const hideConfirmation = () => {
    setConfirmationModal({
      visible: false,
      type: '',
      taskId: null,
      taskName: ''
    })
  }

  const handleAcceptTask = (taskId) => {
    const task = tasks.find(t => t.id === taskId)
    showConfirmation('accept', taskId, task?.patientName || '')
  }

  const handleRejectTask = (taskId) => {
    const task = tasks.find(t => t.id === taskId)
    showConfirmation('reject', taskId, task?.patientName || '')
  }

  const handlePickupSample = (taskId) => {
    const task = tasks.find(t => t.id === taskId)
    showConfirmation('pickup', taskId, task?.patientName || '')
  }

  const confirmAction = () => {
    if (confirmationModal.type === 'accept') {
      setTasks(currentTasks =>
        currentTasks.map(task =>
          task.id === confirmationModal.taskId ? { ...task, status: 'accepted' } : task
        )
      )
    } else if (confirmationModal.type === 'reject') {
      setTasks(currentTasks =>
        currentTasks.filter(task => task.id !== confirmationModal.taskId)
      )
    } else if (confirmationModal.type === 'pickup') {
      setTasks(currentTasks =>
        currentTasks.map(task =>
          task.id === confirmationModal.taskId ? { ...task, status: 'completed' } : task
        )
      )
    }
    hideConfirmation()
  }

  const filteredTasks = useMemo(() => {
    return tasks.filter(task => task.status === activeTab.toLowerCase())
  }, [tasks, activeTab])

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'pending': return Colors.pendingColor
      case 'accepted': return Colors.info
      case 'completed': return Colors.success
      default: return Colors.error
    }
  }

  const getTaskCount = (status) => {
    return tasks.filter(task => task.status === status.toLowerCase()).length
  }

  const handleNavigateToAddress = (address) => {
    const encodedAddress = encodeURIComponent(address)
    
    let url
    if (Platform.OS === 'ios') {
      url = `maps://app?daddr=${encodedAddress}`
    } else {
      url = `google.navigation:q=${encodedAddress}`
    }
    
    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url)
        } else {
          // Fallback to web-based maps
          const webUrl = `https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`
          return Linking.openURL(webUrl)
        }
      })
      .catch((err) => {
        Alert.alert(
          'Navigation Error',
          'Unable to open navigation app. Please check if you have a maps application installed.',
          [{ text: 'OK' }]
        )
        console.error('Navigation error:', err)
      })
  }

  const toggleCardExpansion = (taskId) => {
    setExpandedCards(prev => ({
      ...prev,
      [taskId]: !prev[taskId]
    }))
  }

  const renderTestDetail = (test) => (
    <View key={test.id} style={styles.testDetailCard}>
      <View style={styles.testHeader}>
        <Text style={styles.testName}>{test.name}</Text>
        <Text style={styles.testCode}>{test.SLDCode}</Text>
      </View>
      
      <View style={styles.testInfo}>
        <View style={styles.infoRow}>
          <Icon name="information-outline" size={responsiveFontSize(1.6)} color={Colors.info} />
          <Text style={styles.infoText}>{test.pre_test_info}</Text>
        </View>
        
        <View style={styles.infoRow}>
          <Icon name="test-tube" size={responsiveFontSize(1.6)} color={Colors.primary} />
          <Text style={styles.infoText}>{test.specimen_details}</Text>
        </View>
        
        {test.fasting_required === 'active' && (
          <View style={styles.fastingBadge}>
            <Icon name="clock-alert-outline" size={responsiveFontSize(1.4)} color={Colors.warning} />
            <Text style={styles.fastingText}>Fasting Required</Text>
          </View>
        )}
        
        <View style={styles.specialityRow}>
          <Icon name="doctor" size={responsiveFontSize(1.6)} color={Colors.success} />
          <Text style={styles.specialityText}>{test.speciality.name}</Text>
        </View>
        
        <View style={styles.containerSection}>
          <Text style={styles.containerLabel}>Container Types:</Text>
          <View style={styles.containerList}>
            {test.containerType.map((container) => (
              <View key={container.id} style={styles.containerItem}>
                <View style={[styles.colorDot, { backgroundColor: container.tubeColor }]} />
                <Text style={styles.containerName}>{container.name}</Text>
              </View>
            ))}
          </View>
        </View>
      </View>
    </View>
  )

  const renderTaskItem = ({ item }) => {
    const isExpanded = expandedCards[item.id]
    
    return (
      <Pressable style={styles.taskCard}>
        <Pressable onPress={() => toggleCardExpansion(item.id)} style={styles.taskHeader}>
          <View style={styles.patientSection}>
            <View style={styles.avatarContainer}>
              <Icon name="account" size={responsiveFontSize(2.5)} color={Colors.primary} />
            </View>
            <View style={styles.patientInfo}>
              <Text style={styles.patientName}>{item.patientName}</Text>
              <View style={styles.timeRow}>
                <Icon name="clock-outline" size={responsiveFontSize(1.4)} color={Colors.primary} />
                <Text style={styles.taskTime}>{item.time}</Text>
                <Text style={styles.taskDate}>• {item.date}</Text>
              </View>
            </View>
          </View>
          <View style={styles.headerRight}>
            <View style={[styles.statuspriorityBadge, { backgroundColor: `${getPriorityColor(item.status)}18` }]}>
              <Text style={[styles.statusText, { color: getPriorityColor(item.status) }]}>
                {item.status}
              </Text>
            </View>
          </View>
        </Pressable>
        
        <Pressable onPress={() => toggleCardExpansion(item.id)} style={styles.taskDetailRow}>
            <Icon name="map-marker-outline" size={responsiveFontSize(2)} color={Colors.textSecondary} />
            <Text style={styles.taskAddress} numberOfLines={isExpanded ? undefined : 2}>{item.address}</Text>
        </Pressable>

        {item.testDetails && (
          <Pressable onPress={() => toggleCardExpansion(item.id)} style={styles.testSummary}>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: responsiveWidth(2)}}>
              <Icon name="flask-outline" size={responsiveFontSize(1.8)} color={Colors.info} />
              <Text style={styles.testCount}>{item.testDetails.length} Test(s) Required</Text>
            </View>
            <View style={styles.expandButton}>
              <Icon 
                name={isExpanded ? "chevron-up" : "chevron-down"} 
                size={responsiveFontSize(2.5)} 
                color={Colors.primary} 
              />
            </View>
          </Pressable>
        )}

        {isExpanded && item.testDetails && (
          <View style={styles.expandedContent}>
            <Text style={styles.testDetailsTitle}>Test Details:</Text>
            {item.testDetails.map(renderTestDetail)}
          </View>
        )}
        
        {item.status === 'pending' && (
          <View style={styles.actionContainer}>
            <TouchableOpacity
              style={styles.acceptButton}
              onPress={() => handleAcceptTask(item.id)}
            >
              <Icon name="check" size={responsiveFontSize(2)} color={Colors.white} />
              <Text style={styles.acceptButtonText}>Accept</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.rejectButton}
              onPress={() => handleRejectTask(item.id)}
            >
              <Icon name="close" size={responsiveFontSize(2)} color={Colors.error} />
              <Text style={styles.rejectButtonText}>Reject</Text>
            </TouchableOpacity>
          </View>
        )}
        
        {item.status === 'accepted' && (
          <View style={styles.acceptedContainer}>
            <View style={styles.statusInfo}>
              <Icon name="check-circle" size={responsiveFontSize(2)} color={Colors.success} />
              <Text style={styles.acceptedText}>Task Accepted</Text>
            </View>
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={styles.startButton}
                onPress={() => handleNavigateToAddress(item.address)}
              >
                <Icon name="navigation" size={responsiveFontSize(1.8)} color={Colors.white} />
                <Text style={styles.startButtonText}>Navigate</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.pickupButton}
                onPress={() => handlePickupSample(item.id)}
              >
                <Icon name="test-tube" size={responsiveFontSize(1.8)} color={Colors.white} />
                <Text style={styles.pickupButtonText}>Sample Picked</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
        
        {item.status === 'completed' && (
          <View style={[styles.statusBadge, { backgroundColor: `${Colors.success}20` }]}>
            <Icon name="check-circle" size={responsiveFontSize(2)} color={Colors.success} />
            <Text style={[styles.statusBadgeText, { color: Colors.success }]}>Completed</Text>
          </View>
        )}
        
        {item.status === 'cancelled' && (
          <View style={[styles.statusBadge, { backgroundColor: `${Colors.error}20` }]}>
            <Icon name="close-circle" size={responsiveFontSize(2)} color={Colors.error} />
            <Text style={[styles.statusBadgeText, { color: Colors.error }]}>Cancelled</Text>
          </View>
        )}
      </Pressable>
    )
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={[Colors.primary, '#4A90E2']}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>My Tasks</Text>
        <Text style={styles.headerSubtitle}>Manage your daily assignments</Text>
      </LinearGradient>

      {/* Tab Container */}
      <View style={styles.tabContainer}>
        {tabs.map(tab => (
          <TouchableOpacity
            key={tab}
            style={[styles.tab, activeTab === tab && styles.activeTab]}
            onPress={() => setActiveTab(tab)}
          >
            <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
              {tab}
            </Text>
            <View style={[styles.tabBadge, activeTab === tab && styles.activeTabBadge]}>
              <Text style={[styles.tabBadgeText, activeTab === tab && styles.activeTabBadgeText]}>
                {getTaskCount(tab)}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      <FlatList
        data={filteredTasks}
        keyExtractor={item => item.id}
        renderItem={renderTaskItem}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="clipboard-text-outline" size={responsiveFontSize(8)} color={Colors.textSecondary} />
            <Text style={styles.emptyText}>No tasks found for this category</Text>
            <Text style={styles.emptySubText}>Check back later for new assignments</Text>
          </View>
        }
      />

      {/* Confirmation Modal */}
      <ConfirmationModal
        visible={confirmationModal.visible}
        type={confirmationModal.type}
        title={
          confirmationModal.type === 'accept' ? 'Accept Task' :
          confirmationModal.type === 'reject' ? 'Reject Task' :
          confirmationModal.type === 'pickup' ? 'Confirm Sample Pickup' : 'Confirm Action'
        }
        message={
          confirmationModal.type === 'accept' 
            ? `Are you sure you want to accept the task for ${confirmationModal.taskName}?`
            : confirmationModal.type === 'reject'
            ? `Are you sure you want to reject the task for ${confirmationModal.taskName}? This action cannot be undone.`
            : confirmationModal.type === 'pickup'
            ? `Confirm that you have successfully picked up the blood sample from ${confirmationModal.taskName}.`
            : 'Are you sure you want to perform this action?'
        }
        confirmText={
          confirmationModal.type === 'accept' ? 'Accept' :
          confirmationModal.type === 'reject' ? 'Reject' :
          confirmationModal.type === 'pickup' ? 'Confirm Pickup' : 'Confirm'
        }
        onConfirm={confirmAction}
        onCancel={hideConfirmation}
      />
    </SafeAreaView>
  )
}

export default Task

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    paddingHorizontal: responsiveWidth(5),
    paddingVertical: responsiveHeight(3),
    borderBottomLeftRadius: responsiveWidth(6),
    borderBottomRightRadius: responsiveWidth(6),
  },
  headerTitle: {
    fontSize: responsiveFontSize(2.8),
    fontWeight: 'bold',
    color: Colors.white,
  },
  headerSubtitle: {
    fontSize: responsiveFontSize(1.6),
    color: 'rgba(255,255,255,0.8)',
    marginTop: responsiveHeight(0.5),
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    marginHorizontal: responsiveWidth(5),
    marginTop: responsiveHeight(2),
    borderRadius: responsiveWidth(3),
    padding: responsiveWidth(1),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: responsiveHeight(1.2),
    paddingHorizontal: responsiveWidth(3),
    borderRadius: responsiveWidth(2.5),
    gap: responsiveWidth(1),
  },
  activeTab: {
    backgroundColor: Colors.primary,
  },
  tabText: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.textSecondary,
    fontWeight: '600',
  },
  activeTabText: {
    color: Colors.white,
  },
  tabBadge: {
    backgroundColor: Colors.background,
    borderRadius: responsiveWidth(2.5),
    paddingHorizontal: responsiveWidth(2),
    paddingVertical: responsiveHeight(0.2),
    minWidth: responsiveWidth(5),
    alignItems: 'center',
  },
  activeTabBadge: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  tabBadgeText: {
    fontSize: responsiveFontSize(1.2),
    color: Colors.textSecondary,
    fontWeight: 'bold',
  },
  activeTabBadgeText: {
    color: Colors.white,
  },
  listContent: {
    padding: responsiveWidth(5),
  },
  taskCard: {
    backgroundColor: Colors.surface,
    borderRadius: responsiveWidth(4),
    padding: responsiveWidth(4),
    marginBottom: responsiveHeight(2),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: responsiveHeight(1.5),
  },
  patientSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    width: responsiveWidth(12),
    height: responsiveWidth(12),
    borderRadius: responsiveWidth(6),
    backgroundColor: `${Colors.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: responsiveWidth(3),
  },
  patientInfo: {
    flex: 1,
  },
  patientName: {
    fontSize: responsiveFontSize(1.9),
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: responsiveHeight(0.3),
  },
  timeRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  taskTime: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.primary,
    fontWeight: '600',
    marginLeft: responsiveWidth(1),
  },
  taskDate: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.textSecondary,
    fontWeight: '500',
    marginLeft: responsiveWidth(1),
  },
  priorityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    paddingHorizontal: responsiveWidth(2.5),
    paddingVertical: responsiveHeight(0.5),
    borderRadius: responsiveWidth(3),
    gap: responsiveWidth(1),
  },
  priorityDot: {
    width: responsiveWidth(2),
    height: responsiveWidth(2),
    borderRadius: responsiveWidth(1),
  },
  priorityText: {
    fontSize: responsiveFontSize(1.2),
    fontWeight: 'bold',
  },
  taskDetailRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: responsiveHeight(2),
    paddingHorizontal: responsiveWidth(1),
  },
  taskAddress: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.textSecondary,
    marginLeft: responsiveWidth(2),
    flex: 1,
    lineHeight: responsiveFontSize(2.2),
  },
  actionContainer: {
    flexDirection: 'row',
    gap: responsiveWidth(3),
  },
  acceptButton: {
    backgroundColor: Colors.primary,
    paddingVertical: responsiveHeight(1.3),
    paddingHorizontal: responsiveWidth(4),
    borderRadius: responsiveWidth(3),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    gap: responsiveWidth(2),
  },
  acceptButtonText: {
    color: Colors.white,
    fontSize: responsiveFontSize(1.7),
    fontWeight: 'bold',
  },
  rejectButton: {
    backgroundColor: Colors.white,
    paddingVertical: responsiveHeight(1.3),
    paddingHorizontal: responsiveWidth(4),
    borderRadius: responsiveWidth(3),
    borderWidth: 1.5,
    borderColor: Colors.error,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    gap: responsiveWidth(2),
  },
  rejectButtonText: {
    color: Colors.error,
    fontSize: responsiveFontSize(1.7),
    fontWeight: 'bold',
  },
  acceptedContainer: {
    backgroundColor: `${Colors.success}15`,
    paddingVertical: responsiveHeight(1.5),
    paddingHorizontal: responsiveWidth(3),
    borderRadius: responsiveWidth(3),
  },
  statusInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(2),
    marginBottom: responsiveHeight(1),
  },
  acceptedText: {
    color: Colors.success,
    fontWeight: 'bold',
    fontSize: responsiveFontSize(1.6),
  },
  actionButtons: {
    flexDirection: 'row',
    gap: responsiveWidth(2),
  },
  startButton: {
    backgroundColor: Colors.success,
    paddingVertical: responsiveHeight(1.3),
    paddingHorizontal: responsiveWidth(4),
    borderRadius: responsiveWidth(3),
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(2),
    flex: 1,
    justifyContent: 'center',
  },
  startButtonText: {
    color: Colors.white,
    fontSize: responsiveFontSize(1.4),
    fontWeight: 'bold',
  },
  pickupButton: {
    backgroundColor: Colors.primary,
    paddingVertical: responsiveHeight(1.3),
    paddingHorizontal: responsiveWidth(4),
    borderRadius: responsiveWidth(3),
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(2),
    flex: 1,
    justifyContent: 'center',
  },
  pickupButtonText: {
    color: Colors.white,
    fontSize: responsiveFontSize(1.4),
    fontWeight: 'bold',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: responsiveHeight(1.2),
    borderRadius: responsiveWidth(3),
    gap: responsiveWidth(2),
  },
  statusBadgeText: {
    fontSize: responsiveFontSize(1.7),
    fontWeight: 'bold',
  },
  emptyContainer: {
    alignItems: 'center',
    marginTop: responsiveHeight(8),
    paddingHorizontal: responsiveWidth(8),
  },
  emptyText: {
    fontSize: responsiveFontSize(2),
    color: Colors.textPrimary,
    fontWeight: '600',
    marginTop: responsiveHeight(2),
    textAlign: 'center',
  },
  emptySubText: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.textSecondary,
    marginTop: responsiveHeight(1),
    textAlign: 'center',
  },
  statuspriorityBadge: {
    paddingHorizontal: responsiveWidth(3),
    paddingVertical: responsiveHeight(0.5),
    borderRadius: responsiveWidth(3),
  },
  statusText: {
    fontSize: responsiveFontSize(1.3),
    fontWeight: 'bold',
    textTransform: 'capitalize',
    width: responsiveWidth(13),
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(2),
  },
  expandButton: {
    padding: responsiveWidth(1),
  },
  testSummary: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: responsiveWidth(2),
    marginBottom: responsiveHeight(1.5),
    paddingVertical: responsiveHeight(0.8),
    paddingHorizontal: responsiveWidth(3),
    backgroundColor: `${Colors.info}10`,
    borderRadius: responsiveWidth(2),
  },
  testCount: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.info,
    fontWeight: '600',
  },
  expandedContent: {
    marginTop: responsiveHeight(1),
    paddingTop: responsiveHeight(1.5),
    borderTopWidth: 1,
    borderTopColor: Colors.gray + '30',
  },
  testDetailsTitle: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: responsiveHeight(1.5),
  },
  testDetailCard: {
    backgroundColor: Colors.background,
    borderRadius: responsiveWidth(3),
    padding: responsiveWidth(3),
    marginBottom: responsiveHeight(1.5),
    borderLeftWidth: 3,
    borderLeftColor: Colors.primary,
  },
  testHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: responsiveHeight(1),
  },
  testName: {
    fontSize: responsiveFontSize(1.7),
    fontWeight: 'bold',
    color: Colors.textPrimary,
    flex: 1,
  },
  testCode: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.primary,
    fontWeight: '600',
    backgroundColor: `${Colors.primary}15`,
    paddingHorizontal: responsiveWidth(2),
    paddingVertical: responsiveHeight(0.3),
    borderRadius: responsiveWidth(1.5),
  },
  testInfo: {
    gap: responsiveHeight(0.8),
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: responsiveWidth(2),
  },
  infoText: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.textSecondary,
    flex: 1,
    lineHeight: responsiveHeight(2.2),
  },
  fastingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(1.5),
    backgroundColor: `${Colors.warning}15`,
    paddingHorizontal: responsiveWidth(2.5),
    paddingVertical: responsiveHeight(0.5),
    borderRadius: responsiveWidth(2),
    alignSelf: 'flex-start',
  },
  fastingText: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.warning,
    fontWeight: '600',
  },
  specialityRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(2),
  },
  specialityText: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.success,
    fontWeight: '600',
  },
  containerSection: {
    marginTop: responsiveHeight(0.5),
  },
  containerLabel: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.textPrimary,
    fontWeight: '600',
    marginBottom: responsiveHeight(0.5),
  },
  containerList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: responsiveWidth(2),
  },
  containerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(1.5),
    backgroundColor: Colors.surface,
    paddingHorizontal: responsiveWidth(2),
    paddingVertical: responsiveHeight(0.4),
    borderRadius: responsiveWidth(1.5),
    borderWidth: 1,
    borderColor: Colors.gray + '30',
  },
  colorDot: {
    width: responsiveWidth(3),
    height: responsiveWidth(3),
    borderRadius: responsiveWidth(1.5),
  },
  containerName: {
    fontSize: responsiveFontSize(1.3),
    color: Colors.textSecondary,
    fontWeight: '500',
  },
})
