import React, { useRef, useEffect } from 'react'
import { View, Text, StyleSheet, Modal, TouchableOpacity, Animated } from 'react-native'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import Icon from 'react-native-vector-icons/MaterialCommunityIcons'
import Colors from '../styles/Colors'

const ConfirmationModal = ({
  visible,
  type = 'confirm', // 'accept', 'reject', 'confirm', 'delete'
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  icon,
  confirmColor,
  paymentStatus,
  paymentMethod,
  totalAmount,
}) => {
  const scaleAnim = useRef(new Animated.Value(0)).current

  useEffect(() => {
    if (visible) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start()
    } else {
      Animated.timing(scaleAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start()
    }
  }, [visible])

  const getTypeConfig = () => {
    switch (type) {
      case 'accept':
        return {
          icon: 'check-circle',
          color: Colors.success,
          backgroundColor: `${Colors.success}20`,
        }
      case 'reject':
      case 'pickup':
        return {
          icon: 'truck-delivery-outline',
          color: Colors.primary,
          backgroundColor: `${Colors.primary}20`,
        }
      case 'delete':
        return {
          icon: 'alert-circle',
          color: Colors.error,
          backgroundColor: `${Colors.error}20`,
        }
      default:
        return {
          icon: 'help-circle',
          color: Colors.primary,
          backgroundColor: `${Colors.primary}20`,
        }
    }
  }

  const config = getTypeConfig()
  const finalIcon = icon || config.icon
  const finalColor = confirmColor || config.color

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View style={styles.modalOverlay}>
        <Animated.View 
          style={[
            styles.modalContainer,
            {
              transform: [{ scale: scaleAnim }]
            }
          ]}
        >
          <View style={styles.modalHeader}>
            <View style={[
              styles.modalIcon,
              { backgroundColor: config.backgroundColor }
            ]}>
              <Icon 
                name={finalIcon} 
                size={responsiveFontSize(4)} 
                color={finalColor} 
              />
            </View>
            <Text style={styles.modalTitle}>{title}</Text>
            <Text style={styles.modalMessage}>{message}</Text>
          </View>

          {paymentMethod && totalAmount && (
            <View style={styles.paymentContainer}>
              <Text style={styles.amountText}>₹{totalAmount.toLocaleString()}/ {paymentMethod}</Text>
            </View>
          )}

          {paymentStatus && (
            <View style={styles.paymentStatusBadge}>
              <Text style={styles.paymentStatusText}>Payment Status: </Text>
              <Icon
                name={paymentStatus === 'Paid' ? "check-circle" : "clock-outline"}
                size={responsiveFontSize(1.4)}
                color={paymentStatus === 'Paid' ? Colors.success : Colors.error}
              />
              <Text style={[styles.paymentStatusText, {color: paymentStatus === 'Paid' ? Colors.success : Colors.error}]}>
                {paymentStatus}
              </Text>
            </View>
          )}

          
          <View style={styles.modalActions}>
            <TouchableOpacity 
              style={styles.cancelButton}
              onPress={onCancel}
            >
              <Text style={styles.cancelButtonText}>{cancelText}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.confirmButton,
                { backgroundColor: finalColor }
              ]}
              onPress={onConfirm}
            >
              <Text style={styles.confirmButtonText}>{confirmText}</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  )
}

export default ConfirmationModal

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: responsiveWidth(8),
  },
  modalContainer: {
    backgroundColor: Colors.surface,
    borderRadius: responsiveWidth(5),
    padding: responsiveWidth(6),
    width: '100%',
    maxWidth: responsiveWidth(85),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: responsiveHeight(3),
  },
  modalIcon: {
    width: responsiveWidth(16),
    height: responsiveWidth(16),
    borderRadius: responsiveWidth(8),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: responsiveHeight(2),
  },
  modalTitle: {
    fontSize: responsiveFontSize(2.2),
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: responsiveHeight(1),
    textAlign: 'center',
  },
  modalMessage: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: responsiveFontSize(2.2),
  },
  modalActions: {
    flexDirection: 'row',
    gap: responsiveWidth(3),
  },
  cancelButton: {
    flex: 1,
    paddingVertical: responsiveHeight(1.5),
    borderRadius: responsiveWidth(3),
    borderWidth: 1.5,
    borderColor: Colors.textSecondary,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: responsiveFontSize(1.7),
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  confirmButton: {
    flex: 1,
    paddingVertical: responsiveHeight(1.5),
    borderRadius: responsiveWidth(3),
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: responsiveFontSize(1.7),
    fontWeight: 'bold',
    color: Colors.white,
  },
  paymentStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(1),
    paddingHorizontal: responsiveWidth(0.5),
    paddingVertical: responsiveHeight(0.5),
    borderRadius: responsiveWidth(2.5),
    marginBottom: responsiveHeight(2),
  },
  paymentStatusText: {
    fontSize: responsiveFontSize(1.4),
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  paymentContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // marginBottom: responsiveHeight(1.5),
    paddingHorizontal: responsiveWidth(1),
  },
  amountText: {
    fontSize: responsiveFontSize(1.6),
    fontWeight: '700',
    color: Colors.black,
  },
})