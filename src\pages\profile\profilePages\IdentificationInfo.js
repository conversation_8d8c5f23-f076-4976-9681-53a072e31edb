import { View, Text, StyleSheet, ScrollView, Alert, KeyboardAvoidingView, TouchableOpacity } from 'react-native'
import React, { useState } from 'react'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import InputBox from '../../../components/InputBox'
import Colors from '../../../styles/Colors'
import CustumBtn from '../../../components/CustumBtn'
import MyHeader from '../../../components/MyHeader'
import PickImageComponent from '../../../components/PickImageComponent';

const IdentificationInfo = ({navigation}) => {
  // Identification & Verification
  const [aadharNumber, setAadharNumber] = useState('')
  const [panNumber, setPanNumber] = useState('')
  const [aadharDocument, setAadharDocument] = useState(null)
  const [aadharBack, setAadharBack] = useState(null)
  const [panDocument, setPanDocument] = useState(null)
  const [loading, setLoading] = useState(false)

  const handlePickDocument = async (documentType) => {
    try {
      const document = await PickImageComponent();
      if (document) {
        switch (documentType) {
          case 'aadhar':
            setAadharDocument(document);
            console.log('Selected aadhar:', document.uri);
            break;
          case 'aadharBack':
            setAadharBack(document);
            console.log('Selected aadharBack:', document.uri);
            break;
          case 'pan':
            setPanDocument(document);
            console.log('Selected pan:', document);
            break;
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick document');
    }
  }

  const DocumentUploadBox = ({ label, document, onPress, icon = "file-document-outline" }) => (
    <TouchableOpacity onPress={onPress} style={styles.documentUploadBox}>
      <View style={styles.documentUploadContent}>
        <MaterialCommunityIcons name={icon} size={responsiveFontSize(2.5)} color={Colors.primary} />
        <View style={styles.documentUploadText}>
          <Text style={styles.documentUploadLabel}>{label}</Text>
          <Text style={styles.documentUploadStatus}>
            {document ? document.fileName || 'Document Selected' : 'Tap to upload'}
          </Text>
        </View>
        <MaterialCommunityIcons 
          name={document ? "check-circle" : "upload"} 
          size={responsiveFontSize(2)} 
          color={document ? Colors.green : Colors.gray} 
        />
      </View>
    </TouchableOpacity>
  );

  const handleSaveChanges = () => {
    // Basic validation
    if (!aadharNumber || aadharNumber.length !== 12) {
      Alert.alert('Error', 'Please enter a valid 12-digit Aadhar number');
      return;
    }
    if (!panNumber || panNumber.length !== 10) {
      Alert.alert('Error', 'Please enter a valid 10-character PAN number');
      return;
    }

    setLoading(true)
    // Simulate API call
    setTimeout(() => {
      setLoading(false)
      Alert.alert('Success', 'Identification information updated successfully!')
    }, 2000)
  }

  return (
    <View style={styles.container}>
      <MyHeader title="Identification & Verification" onBackPress={() => navigation.goBack()} />
      <KeyboardAvoidingView behavior="padding" style={{ flex: 1 }}>
        <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={styles.contentContainer}>
          <View style={styles.content}>
            
            <InputBox
              label="Aadhar Card Number"
              value={aadharNumber}
              onChangeText={setAadharNumber}
              leftIcon="card-account-details-outline"
              placeholder="Enter 12-digit Aadhar number"
              keyboardType="numeric"
              maxLength={12}
            />

            <DocumentUploadBox
              label="Upload Aadhar Front (PDF/JPG)"
              document={aadharDocument}
              onPress={() => handlePickDocument('aadhar')}
              icon="card-account-details"
            />

            <DocumentUploadBox
              label="Upload Aadhar Back (PDF/JPG)"
              document={aadharBack}
              onPress={() => handlePickDocument('aadharBack')}
              icon="card-account-details"
            />

            <InputBox
              label="PAN Card Number"
              value={panNumber}
              onChangeText={setPanNumber}
              leftIcon="credit-card-outline"
              placeholder="Enter PAN number"
              autoCapitalize="characters"
              maxLength={10}
            />

            <DocumentUploadBox
              label="Upload PAN Copy (PDF/JPG)"
              document={panDocument}
              onPress={() => handlePickDocument('pan')}
              icon="credit-card"
            />
            
            <CustumBtn
              title="Save Changes"
              onPress={handleSaveChanges}
              loading={loading}
              disabled={loading}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>  
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  contentContainer: {
    flexGrow: 1,
  },
  content: {
    padding: responsiveWidth(5),
    gap: responsiveHeight(2)
  },
  documentUploadBox: {
    borderWidth: 1,
    borderColor: Colors.gray,
    borderRadius: 8,
    backgroundColor: Colors.surface,
    marginBottom: responsiveHeight(1),
  },
  documentUploadContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: responsiveWidth(4),
    gap: responsiveWidth(3),
  },
  documentUploadText: {
    flex: 1,
  },
  documentUploadLabel: {
    fontSize: responsiveFontSize(1.6),
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  documentUploadStatus: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.textSecondary,
  },
})

export default IdentificationInfo
