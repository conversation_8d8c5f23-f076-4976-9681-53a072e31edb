import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native'
import React from 'react'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Colors from '../../../styles/Colors'
import MyHeader from '../../../components/MyHeader'

const ProfileManagement = ({navigation}) => {

  const profileSections = [
    {
      id: 1,
      title: 'Personal Information',
      subtitle: 'Basic profile details',
      icon: 'account-outline',
      route: 'EditProfile',
      color: Colors.primary
    },
    {
      id: 2,
      title: 'Professional Information',
      subtitle: 'Qualifications, certifications & experience',
      icon: 'school-outline',
      route: 'ProfessionalInfo',
      color: '#2196F3'
    },
    {
      id: 3,
      title: 'Identification & Verification',
      subtitle: 'Aadhar & PAN card details',
      icon: 'card-account-details-outline',
      route: 'IdentificationInfo',
      color: '#FF9800'
    },
    {
      id: 4,
      title: 'Bank Details',
      subtitle: 'For payout/commission',
      icon: 'bank-outline',
      route: 'BankDetails',
      color: '#4CAF50'
    },
    {
      id: 5,
      title: 'Device & Availability',
      subtitle: 'Work preferences & device info',
      icon: 'cellphone-settings',
      route: 'DeviceAvailabilityInfo',
      color: '#9C27B0'
    }
  ];

  const ProfileCard = ({ item }) => (
    <TouchableOpacity 
      style={styles.profileCard}
      onPress={() => navigation.navigate(item.route)}
      activeOpacity={0.7}
    >
      <View style={[styles.iconContainer, { backgroundColor: item.color + '20' }]}>
        <MaterialCommunityIcons 
          name={item.icon} 
          size={responsiveFontSize(3)} 
          color={item.color} 
        />
      </View>
      <View style={styles.cardContent}>
        <Text style={styles.cardTitle}>{item.title}</Text>
        <Text style={styles.cardSubtitle}>{item.subtitle}</Text>
      </View>
      <MaterialCommunityIcons 
        name="chevron-right" 
        size={responsiveFontSize(2.5)} 
        color={Colors.gray} 
      />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <MyHeader title="Profile Management" onBackPress={() => navigation.goBack()} />
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={styles.contentContainer}>
        <View style={styles.content}>
          
          <Text style={styles.headerText}>Manage Your Profile</Text>
          <Text style={styles.subHeaderText}>
            Complete your profile to get first order
          </Text>

          <View style={styles.cardsContainer}>
            {profileSections.map((item) => (
              <ProfileCard key={item.id} item={item} />
            ))}
          </View>

          <View style={styles.infoBox}>
            <MaterialCommunityIcons 
              name="information-outline" 
              size={responsiveFontSize(2)} 
              color={Colors.primary} 
            />
            <Text style={styles.infoText}>
              Complete all sections to get your first order.
            </Text>
          </View>

        </View>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  contentContainer: {
    flexGrow: 1,
  },
  content: {
    padding: responsiveWidth(5),
  },
  headerText: {
    fontSize: responsiveFontSize(2.5),
    fontWeight: 'bold',
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: responsiveHeight(1),
  },
  subHeaderText: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: responsiveHeight(3),
  },
  cardsContainer: {
    gap: responsiveHeight(1.5),
  },
  profileCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    padding: responsiveWidth(4),
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.gray + '30',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  iconContainer: {
    width: responsiveWidth(12),
    height: responsiveWidth(12),
    borderRadius: responsiveWidth(6),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: responsiveWidth(4),
  },
  cardContent: {
    flex: 1,
  },
  cardTitle: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  cardSubtitle: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.textSecondary,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.primary + '10',
    padding: responsiveWidth(4),
    borderRadius: 8,
    marginTop: responsiveHeight(3),
    gap: responsiveWidth(2),
  },
  infoText: {
    flex: 1,
    fontSize: responsiveFontSize(1.4),
    color: Colors.textPrimary,
    lineHeight: responsiveFontSize(2),
  },
})

export default ProfileManagement
