import * as React from 'react'
import { View, Keyboard } from 'react-native'
import { TextInput } from 'react-native-paper'
import Colors from '../styles/Colors'
import { responsiveFontSize, responsiveHeight } from 'react-native-responsive-dimensions'

const InputBox = ({ label, value, onChangeText, leftIcon, ...props }) => {
  const inputRef = React.useRef(null)

  React.useEffect(() => {
    const keyboardListener = Keyboard.addListener('keyboardDidHide', () => {
      if (inputRef.current) {
        inputRef.current.blur()
      }
    })
    return () => {
      keyboardListener.remove()
    }
  }, [])

  return (
    <View>
      <TextInput
        ref={inputRef}
        style={{ fontSize: responsiveFontSize(1.8), padding: 0, backgroundColor: Colors.surface, height: responsiveHeight(5.6) }}
        label={label}
        value={value}
        onChangeText={onChangeText}
        mode="outlined"
        outlineColor={Colors.gray}
        activeOutlineColor={Colors.primary}
        left={leftIcon ? <TextInput.Icon icon={leftIcon} color={Colors.primary} size={responsiveFontSize(2.6)} /> : null}
        theme={{
          colors: {
            primary: Colors.primary,         // label and border when focused
            onSurfaceVariant: Colors.textSecondary,        // placeholder color
            onSurface: Colors.textPrimary,              // text color
          },
          fonts:{
            labelSmall: { fontSize: responsiveFontSize(1) },
            titleSmall: { fontSize: responsiveFontSize(1.2) },
            
          }
        }}
        {...props}
      />
    </View>
  )
}

export default InputBox