import { View, Text, StyleSheet, Image, Alert, TouchableOpacity, ScrollView, KeyboardAvoidingView } from 'react-native'
import React, { useState, useEffect } from 'react'
import { responsiveWidth, responsiveHeight, responsiveFontSize } from 'react-native-responsive-dimensions'
import {
  CodeField,
  Cursor,
  useBlurOnFulfill,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field'
import CustumBtn from '../../components/CustumBtn'
import BottomLink from '../../components/BottomLink'
import Colors from '../../styles/Colors'

const CELL_COUNT = 6

const VerifyOTP = ({ navigation, route }) => {
  const [value, setValue] = useState('')
  const [loading, setLoading] = useState(false)
  const [resendLoading, setResendLoading] = useState(false)
  const [timer, setTimer] = useState(60)
  const [canResend, setCanResend] = useState(false)
  
  const mobile = route?.params?.mobile || '9876543210'
  
  const ref = useBlurOnFulfill({ value, cellCount: CELL_COUNT })
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  })

  useEffect(() => {
    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true)
          clearInterval(interval)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [timer])

  const handleVerifyOTP = () => {
    if (value.length !== CELL_COUNT) {
      Alert.alert('Error', 'Please enter complete 6-digit OTP')
      return
    }

    setLoading(true)
    // Simulate API call
    setTimeout(() => {
      setLoading(false)
      if (value === '123456') {
        Alert.alert('Success', 'OTP verified successfully!', [
          {
            text: 'OK',
            onPress: () => navigation.navigate('Login')
          }
        ])
      } else {
        Alert.alert('Error', 'Invalid OTP. Please try again.')
      }
    }, 2000)
  }

  const handleResendOTP = () => {
    if (!canResend) return

    // setResendLoading(true)
    setTimer(60)
    setCanResend(false)
    setValue('')

    // Simulate API call
    // setTimeout(() => {
    //   setResendLoading(false)
    //   Alert.alert('OTP Sent', 'New OTP has been sent to your mobile number')
    // }, 2000)
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const renderCell = ({ index, symbol, isFocused }) => {
    return (
      <Text
        key={index}
        style={[styles.cell, isFocused && styles.focusCell]}
        onLayout={getCellOnLayoutHandler(index)}
      >
        {symbol || (isFocused ? <Cursor /> : null)}
      </Text>
    )
  }

  return (
    <KeyboardAvoidingView behavior="padding" style={{ flex: 1 }}>
    <ScrollView contentContainerStyle={styles.container}>
      <Image
        source={require('../../accets/images/fullLogoWithoutBg.png')}
        style={styles.logo}
      />
      
      <View style={styles.headerContainer}>
        <Text style={styles.title}>Verify OTP</Text>
        <Text style={styles.subtitle}>
          We've sent a 6-digit verification code to
        </Text>
        <Text style={styles.mobileNumber}>+91 {mobile}</Text>
      </View>

      <View style={styles.otpContainer}>
        <Text style={styles.otpLabel}>Enter OTP</Text>
        <CodeField
          ref={ref}
          {...props}
          value={value}
          onChangeText={setValue}
          cellCount={CELL_COUNT}
          rootStyle={styles.codeFieldRoot}
          keyboardType="number-pad"
          textContentType="oneTimeCode"
          renderCell={renderCell}
        />
      </View>

      <View style={styles.timerContainer}>
        {!canResend ? (
          <Text style={styles.timerText}>
            Resend OTP in {formatTime(timer)}
          </Text>
        ) : (
          <TouchableOpacity onPress={handleResendOTP} disabled={resendLoading}>
            <Text style={[styles.resendText, resendLoading && styles.disabledText]}>
              {resendLoading ? 'Sending...' : 'Resend OTP'}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      <CustumBtn 
        title="Verify OTP" 
        onPress={handleVerifyOTP}
        loading={loading}
        disabled={loading}
        style={styles.verifyButton}
      />

      <View style={styles.bottomContainer}>
        <BottomLink 
          text={'Wrong number? '} 
          subText={'Change'} 
          subTextColor={Colors.primary} 
          navigation={navigation} 
          onPress={() => navigation.goBack()} 
        />
      </View>
    </ScrollView>
    </KeyboardAvoidingView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    paddingHorizontal: responsiveWidth(5),
    paddingVertical: responsiveHeight(2),
  },
  logo: {
    width: responsiveWidth(40),
    height: responsiveHeight(20),
    alignSelf: 'center',
    marginBottom: responsiveHeight(3),
    resizeMode: 'contain',
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: responsiveHeight(4),
  },
  title: {
    fontSize: responsiveFontSize(3),
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: responsiveHeight(1),
  },
  subtitle: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: responsiveHeight(0.5),
  },
  mobileNumber: {
    fontSize: responsiveFontSize(2),
    fontWeight: 'bold',
    color: Colors.primary,
  },
  otpContainer: {
    marginBottom: responsiveHeight(3),
  },
  otpLabel: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.textPrimary,
    marginBottom: responsiveHeight(2),
    textAlign: 'center',
  },
  codeFieldRoot: {
    marginHorizontal: responsiveWidth(5),
  },
  cell: {
    width: responsiveWidth(12),
    height: responsiveHeight(6),
    lineHeight: responsiveHeight(5.5),
    fontSize: responsiveFontSize(2.5),
    fontWeight: 'bold',
    borderWidth: 2,
    borderColor: Colors.gray,
    textAlign: 'center',
    borderRadius: responsiveWidth(2),
    backgroundColor: Colors.surface,
    color: Colors.textPrimary,
  },
  focusCell: {
    borderColor: Colors.primary,
    backgroundColor: `${Colors.primary}10`,
  },
  timerContainer: {
    alignItems: 'center',
    marginBottom: responsiveHeight(3),
  },
  timerText: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.textSecondary,
  },
  resendText: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.primary,
    fontWeight: 'bold',
  },
  disabledText: {
    color: Colors.textSecondary,
  },
  verifyButton: {
    marginBottom: responsiveHeight(2),
  },
  bottomContainer: {
    alignSelf: 'center',
  },
})

export default VerifyOTP
