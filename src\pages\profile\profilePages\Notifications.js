import { View, Text, StyleSheet } from 'react-native'
import React from 'react'
import Colors from '../../../styles/Colors'
import { responsiveFontSize } from 'react-native-responsive-dimensions'

const Notifications = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.text}>You have no new notifications.</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  text: {
    fontSize: responsiveFontSize(2.2),
    color: Colors.textSecondary,
  },
})

export default Notifications