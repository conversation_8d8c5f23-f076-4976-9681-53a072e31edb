import React, { useState } from 'react'
import { View, StyleSheet, Text } from 'react-native'
import { Dropdown } from 'react-native-element-dropdown'
import AntDesign from 'react-native-vector-icons/AntDesign'
import Icon from 'react-native-vector-icons/MaterialCommunityIcons'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import Colors from '../styles/Colors'

const CustomDropdown = ({
  label,
  data,
  value,
  onChange,
  leftIcon, // icon name as string
  backgroundColor = Colors.surface,
  renderLeftIcon = true,
  dropdownPosition = 'auto',
  search = true,
  style,
  ...props
}) => {
  const [isFocus, setIsFocus] = useState(false)

  const renderLabel = () => {
    if (value || isFocus) {
      return (
        <Text style={[
          styles.label,
          isFocus && { color: Colors.primary },
          { backgroundColor }
        ]}>
          {label}
        </Text>
      )
    }
    return null
  }

  const renderItem = item => (
    <View style={styles.item}>
      <Text style={styles.textItem}>{item.label}</Text>
    </View>
  )

  return (
    <View style={[styles.container, { backgroundColor }, style]}>
      {renderLabel()}
      <Dropdown
        style={[
          styles.dropdown,
          isFocus && { borderColor: Colors.primary, borderWidth: responsiveWidth(0.5) }
        ]}
        placeholderStyle={styles.placeholderStyle}
        selectedTextStyle={styles.selectedTextStyle}
        inputSearchStyle={styles.inputSearchStyle}
        iconStyle={styles.iconStyle}
        data={data}
        search={search}
        maxHeight={responsiveHeight(27)}
        labelField="label"
        valueField="value"
        placeholder={!isFocus ? label : '...'}
        searchPlaceholder="Search..."
        value={value}
        onFocus={() => setIsFocus(true)}
        onBlur={() => setIsFocus(false)}
        onChange={item => {
          onChange(item.value)
          setIsFocus(false)
        }}
        renderItem={renderItem}
        dropdownPosition={dropdownPosition}
        renderLeftIcon={
          leftIcon
            ? () => (
                <Icon
                  name={leftIcon}
                  size={responsiveFontSize(2.4)}
                  color={Colors.primary}
                  style={styles.icon}
                />
              )
            : renderLeftIcon
            ? () => (
                <AntDesign
                  style={styles.icon}
                  color={Colors.primary}
                  name="Safety"
                  size={responsiveFontSize(2.4)}
                />
              )
            : undefined
        }
        {...props}
      />
    </View>
  )
}

export default CustomDropdown

const styles = StyleSheet.create({
  container: {},
  dropdown: {
    height: responsiveHeight(6.2),
    borderColor: Colors.gray,
    borderWidth: responsiveWidth(0.3),
    borderRadius: responsiveFontSize(0.6),
    paddingHorizontal: responsiveWidth(4),
    backgroundColor: Colors.surface,
  },
  icon: {
    marginRight: responsiveWidth(5.2),
  },
  label: {
    position: 'absolute',
    left: responsiveWidth(2.3),
    top: responsiveHeight(-1.2),
    zIndex: 999,
    color: Colors.textSecondary,
    paddingHorizontal: responsiveWidth(1.3),
    fontSize: responsiveFontSize(1.6),
  },
  placeholderStyle: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.textSecondary,
  },
  selectedTextStyle: {
    fontSize: responsiveFontSize(1.8),
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    fontSize: responsiveFontSize(1.6),
  },
  item: {
    padding: responsiveFontSize(1),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // height: responsiveHeight(4),
  },
  textItem: {
    color: Colors.black,
    fontSize: responsiveFontSize(1.6),
  },
})