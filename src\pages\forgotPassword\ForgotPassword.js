import { View, Text, StyleSheet, Image, Alert } from 'react-native'
import React, { useState } from 'react'
import { responsiveWidth, responsiveHeight, responsiveFontSize } from 'react-native-responsive-dimensions'
import InputBox from '../../components/InputBox'
import CustumBtn from '../../components/CustumBtn'
import BottomLink from '../../components/BottomLink'
import Colors from '../../styles/Colors'

const ForgotPassword = ({ navigation }) => {
  const [mobile, setMobile] = useState('')
  const [loading, setLoading] = useState(false)

  const handleSendOTP = () => {
    if (!mobile) {
      Alert.alert('Error', 'Please enter your mobile number')
      return
    }
    
    if (mobile.length !== 10) {
      Alert.alert('Error', 'Please enter a valid 10-digit mobile number')
      return
    }

    setLoading(true)
    // Simulate API call
    setTimeout(() => {
      setLoading(false)
      Alert.alert(
        'OTP Sent',
        `OTP has been sent to ${mobile}`,
        [
          {
            text: 'OK',
            onPress: () => {
              // Navigate to OTP verification screen
              navigation.navigate('VerifyOTP', { mobile })
              console.log('Navigate to OTP verification')
            }
          }
        ]
      )
    }, 2000)
  }

  return (
    <View style={styles.container}>
      <Image
        source={require('../../accets/images/fullLogoWithoutBg.png')}
        style={styles.logo}
      />
      
      <View style={styles.headerContainer}>
        <Text style={styles.title}>Forgot Password?</Text>
        <Text style={styles.subtitle}>
          Don't worry! Enter your mobile number and we'll send you an OTP to reset your password.
        </Text>
      </View>

      <View style={styles.formContainer}>
        <InputBox
          label="Mobile Number"
          placeholder="Enter your mobile number"
          value={mobile}
          onChangeText={setMobile}
          keyboardType="phone-pad"
          leftIcon="phone"
          maxLength={10}
        />
        
        <CustumBtn 
          title="Send OTP" 
          onPress={handleSendOTP}
          loading={loading}
          disabled={loading}
          style={styles.sendButton}
        />
      </View>

      <View style={styles.bottomContainer}>
        <BottomLink 
          text={'Remember your password? '} 
          subText={'Login'} 
          subTextColor={Colors.green} 
          navigation={navigation} 
          onPress={() => navigation.goBack()} 
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    paddingHorizontal: responsiveWidth(5),
    paddingVertical: responsiveHeight(2),
  },
  logo: {
    width: responsiveWidth(40),
    height: responsiveHeight(20),
    alignSelf: 'center',
    marginBottom: responsiveHeight(3),
    resizeMode: 'contain',
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: responsiveHeight(4),
  },
  title: {
    fontSize: responsiveFontSize(3),
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: responsiveHeight(1),
  },
  subtitle: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: responsiveHeight(3),
    paddingHorizontal: responsiveWidth(2),
  },
  formContainer: {
    gap: responsiveHeight(2.5),
    marginBottom: responsiveHeight(3),
  },
  sendButton: {
    marginTop: responsiveHeight(1),
  },
  bottomContainer: {
    alignSelf: 'center',
  },
})

export default ForgotPassword
