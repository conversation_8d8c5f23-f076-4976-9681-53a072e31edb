import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Dimensions,
  FlatList,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import Colors from '../../../styles/Colors';
import MyHeader from '../../../components/MyHeader';
import LinearGradient from 'react-native-linear-gradient';

const { width } = Dimensions.get('window');

const Transaction = ({ navigation }) => {
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [currentBalance, setCurrentBalance] = useState(2500);

  const transactions = [
  {
    id: 1,
    title: 'Admin Payout',
    type: 'Collection Charge',
    amount: '₹ 200',
    from: 'Admin',
    to: 'Phlebo',
    status: 'Received',
    date: 'Aug 5, 2025 10:30 AM',
    icon: 'cash-plus',
    color: Colors.green,
  },
  {
    id: 2,
    title: 'COD Payment',
    type: 'COD Collection',
    amount: '₹ 1000',
    from: 'Patient',
    to: 'Phlebo',
    status: 'Received',
    date: 'Aug 4, 2025 2:00 PM',
    icon: 'cash-minus',
    color: Colors.pendingColor,
  },
  {
    id: 3,
    title: 'Payment to Admin',
    type: 'COD Settlement',
    amount: '₹ 1000',
    from: 'Phlebo',
    to: 'Admin',
    status: 'Paid',
    date: 'Aug 4, 2025 5:00 PM',
    icon: 'cash-remove',
    color: Colors.error,
  },
  {
    id: 4,
    title: 'Admin Payout',
    type: 'Collection Charge',
    amount: '₹ 100',
    from: 'Admin',
    to: 'Phlebo',
    status: 'Received',
    date: 'Aug 3, 2025 6:45 PM',
    icon: 'cash-plus',
    color: Colors.green,
  },
  {
    id: 5,
    title: 'COD Payment',
    type: 'COD Collection',
    amount: '₹ 900',
    from: 'Patient',
    to: 'Phlebo',
    status: 'Received',
    date: 'Aug 2, 2025 8:15 AM',
    icon: 'cash-minus',
    color: Colors.pendingColor,
  },
  {
    id: 6,
    title: 'Payment to Admin',
    type: 'COD Settlement',
    amount: '₹ 900',
    from: 'Phlebo',
    to: 'Admin',
    status: 'Paid',
    date: 'Aug 2, 2025 12:00 PM',
    icon: 'cash-remove',
    color: Colors.error,
  },
  {
    id: 7,
    title: 'Admin Payout',
    type: 'Collection Charge',
    amount: '₹ 200',
    from: 'Admin',
    to: 'Phlebo',
    status: 'Received',
    date: 'Aug 1, 2025 11:20 AM',
    icon: 'cash-plus',
    color: Colors.green,
  },
  {
    id: 8,
    title: 'COD Payment',
    type: 'COD Collection',
    amount: '₹ 1500',
    from: 'Patient',
    to: 'Phlebo',
    status: 'Received',
    date: 'Jul 31, 2025 4:10 PM',
    icon: 'cash-minus',
    color: Colors.pendingColor,
  },
  {
    id: 9,
    title: 'Payment to Admin',
    type: 'COD Settlement',
    amount: '₹ 1500',
    from: 'Phlebo',
    to: 'Admin',
    status: 'Paid',
    date: 'Jul 31, 2025 7:00 PM',
    icon: 'cash-remove',
    color: Colors.error,
  },
  {
    id: 10,
    title: 'Admin Payout',
    type: 'Collection Charge',
    amount: '₹ 100',
    from: 'Admin',
    to: 'Phlebo',
    status: 'Received',
    date: 'Jul 30, 2025 9:30 AM',
    icon: 'cash-plus',
    color: Colors.green,
  },
];



  const filterOptions = [
    { label: 'All', value: 'all' },
    { label: 'Received', value: 'Received' },
    { label: 'Paid', value: 'Paid' },
  ];

  const handleReturnCOD = () => {
    console.log('COD amount returned to Admin.');
    alert('COD amount returned to Admin.');
  };

  const filteredTransactions = selectedFilter === 'all'
    ? transactions
    : transactions.filter(t => t.status === selectedFilter);

  const totalReceived = transactions
    .filter(t => t.status === 'Received')
    .reduce((sum, t) => sum + parseInt(t.amount.replace('₹ ', '')), 0);

  const totalPaid = transactions
    .filter(t => t.status === 'Paid')
    .reduce((sum, t) => sum + parseInt(t.amount.replace('₹ ', '')), 0);

  const renderTransactionItem = ({ item }) => (
    <View style={styles.transactionCard}>
      <View style={styles.transactionHeader}>
        <View style={[styles.iconContainer, { backgroundColor: item.color + '15' }]}>
          <Icon name={item.icon} size={responsiveFontSize(2.5)} color={item.color} />
        </View>
        <View style={styles.transactionInfo}>
          <Text style={styles.transactionTitle}>{item.title}</Text>
          <Text style={styles.transactionType}>{item.type}</Text>
          <Text style={styles.transactionDate}>{item.date}</Text>
        </View>
        <View style={styles.amountContainer}>
          <Text style={[styles.transactionAmount, {
            color: item.status === 'Received' ? item.color : Colors.error
          }]}>
            {item.status === 'Received' ? '+' : '-'}{item.amount}
          </Text>
          <View style={[styles.statusBadge, { }]}>
            <Text style={[styles.statusText, { color: item.color }]}>{item.status}</Text>
          </View>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <MyHeader title="Balance & Transactions" onBackPress={() => navigation.goBack()} />

      {/* Balance Overview Header */}
      <LinearGradient
        colors={[Colors.primary, '#0088CC']}
        style={styles.balanceHeader}
      >
        <View style={styles.balanceContent}>
          <Text style={styles.balanceLabel}>My Wallet Balance</Text>
          <Text style={styles.balanceAmount}>₹ 1200</Text>
          <View style={styles.balanceStats}>
            <View style={styles.statItem}>
              <Icon name="trending-up" size={responsiveFontSize(2)} color="rgba(255,255,255,0.8)" />
              <Text style={styles.statLabel}>Today Received</Text>
              <Text style={styles.statValue}>₹ 4500</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Icon name="trending-down" size={responsiveFontSize(2)} color="rgba(255,255,255,0.8)" />
              <Text style={styles.statLabel}>Paid</Text>
              <Text style={styles.statValue}>₹ 0</Text>
            </View>
          </View>
        </View>
      </LinearGradient>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity style={styles.actionButton} onPress={handleReturnCOD}>
          <LinearGradient
            colors={['#FF6B6B', '#FF5252']}
            style={styles.actionGradient}
          >
            <Icon name="cash-refund" size={responsiveFontSize(2.5)} color="white" />
            <Text style={styles.actionText}>Return COD</Text>
          </LinearGradient>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton}>
          <LinearGradient
            colors={['#4CAF50', '#45A049']}
            style={styles.actionGradient}
          >
            <Icon name="download" size={responsiveFontSize(2.5)} color="white" />
            <Text style={styles.actionText}>Download</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        {filterOptions.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.filterTab,
              selectedFilter === option.value && styles.activeFilterTab
            ]}
            onPress={() => setSelectedFilter(option.value)}
          >
            <Text style={[
              styles.filterText,
              selectedFilter === option.value && styles.activeFilterText
            ]}>
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Transaction List */}
      <FlatList
        data={filteredTransactions}
        renderItem={renderTransactionItem}
        keyExtractor={(item) => item.id.toString()}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.transactionsList}
      />
    </SafeAreaView>
  );
};

export default Transaction;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  balanceHeader: {
    paddingHorizontal: responsiveWidth(5),
    paddingVertical: responsiveHeight(3),
    borderBottomLeftRadius: responsiveWidth(8),
    borderBottomRightRadius: responsiveWidth(8),
  },
  balanceContent: {
    alignItems: 'center',
  },
  balanceLabel: {
    fontSize: responsiveFontSize(1.8),
    color: 'rgba(255,255,255,0.9)',
    marginBottom: responsiveHeight(0.5),
  },
  balanceAmount: {
    fontSize: responsiveFontSize(3.5),
    fontWeight: 'bold',
    color: Colors.white,
    marginBottom: responsiveHeight(2),
  },
  balanceStats: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.15)',
    borderRadius: responsiveWidth(4),
    paddingHorizontal: responsiveWidth(6),
    paddingVertical: responsiveHeight(1.5),
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statDivider: {
    width: 1,
    height: responsiveHeight(4),
    backgroundColor: 'rgba(255,255,255,0.3)',
    marginHorizontal: responsiveWidth(4),
  },
  statLabel: {
    fontSize: responsiveFontSize(1.4),
    color: 'rgba(255,255,255,0.8)',
    marginTop: responsiveHeight(0.5),
  },
  statValue: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: '600',
    color: Colors.white,
    marginTop: responsiveHeight(0.2),
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: responsiveWidth(5),
    paddingVertical: responsiveHeight(2),
    gap: responsiveWidth(3),
  },
  actionButton: {
    flex: 1,
  },
  actionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: responsiveHeight(1.5),
    borderRadius: responsiveWidth(3),
    gap: responsiveWidth(2),
  },
  actionText: {
    color: Colors.white,
    fontSize: responsiveFontSize(1.6),
    fontWeight: '600',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: responsiveWidth(5),
    marginBottom: responsiveHeight(2),
    gap: responsiveWidth(2),
  },
  filterTab: {
    flex: 1,
    paddingVertical: responsiveHeight(1.2),
    paddingHorizontal: responsiveWidth(4),
    borderRadius: responsiveWidth(6),
    backgroundColor: Colors.white,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: `${Colors.primary}20`,
  },
  activeFilterTab: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  filterText: {
    fontSize: responsiveFontSize(1.6),
    fontWeight: '500',
    color: Colors.textSecondary,
  },
  activeFilterText: {
    color: Colors.white,
  },
  transactionsList: {
    paddingHorizontal: responsiveWidth(5),
    paddingBottom: responsiveHeight(2),
  },
  transactionCard: {
    backgroundColor: Colors.white,
    borderRadius: responsiveWidth(4),
    marginBottom: responsiveHeight(1.5),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  transactionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: responsiveWidth(4),
  },
  iconContainer: {
    width: responsiveWidth(12),
    height: responsiveWidth(12),
    borderRadius: responsiveWidth(6),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: responsiveWidth(3),
  },
  transactionInfo: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: responsiveHeight(0.3),
  },
  transactionType: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.textSecondary,
    marginBottom: responsiveHeight(0.3),
  },
  transactionDate: {
    fontSize: responsiveFontSize(1.3),
    color: Colors.gray,
  },
  amountContainer: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: 'bold',
    marginBottom: responsiveHeight(0.3),
  },
  statusBadge: {
    // paddingHorizontal: responsiveWidth(2.5),
    paddingVertical: responsiveHeight(0.3),
    borderRadius: responsiveWidth(3),
  },
  statusText: {
    fontSize: responsiveFontSize(1.2),
    fontWeight: '600',
    color: Colors.black
  },
});
