import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import Colors from '../../../styles/Colors';

const Transaction = () => {
  const transactions = [
  {
    id: 1,
    title: 'Admin Payout',
    type: 'Collection Charge',
    amount: '₹ 200',
    from: 'Admin',
    to: 'Phlebo',
    direction: 'in',
    status: 'Received',
    date: 'Aug 5, 2025 10:30 AM',
    icon: 'cash-plus',
    color: Colors.green,
  },
  {
    id: 2,
    title: 'COD Payment',
    type: 'COD Collection',
    amount: '₹ 1000',
    from: 'Patient',
    to: 'Phlebo',
    direction: 'in',
    status: 'Received',
    date: 'Aug 4, 2025 2:00 PM',
    icon: 'cash-minus',
    color: Colors.pendingColor,
  },
  {
    id: 3,
    title: 'Payment to Admin',
    type: 'COD Settlement',
    amount: '₹ 1000',
    from: 'Phlebo',
    to: 'Admin',
    direction: 'out',
    status: 'Paid',
    date: 'Aug 4, 2025 5:00 PM',
    icon: 'cash-remove',
    color: Colors.orange,
  },
  {
    id: 4,
    title: 'Admin Payout',
    type: 'Collection Charge',
    amount: '₹ 100',
    from: 'Admin',
    to: 'Phlebo',
    direction: 'in',
    status: 'Received',
    date: 'Aug 3, 2025 6:45 PM',
    icon: 'cash-plus',
    color: Colors.green,
  },
  {
    id: 5,
    title: 'COD Payment',
    type: 'COD Collection',
    amount: '₹ 900',
    from: 'Patient',
    to: 'Phlebo',
    direction: 'in',
    status: 'Received',
    date: 'Aug 2, 2025 8:15 AM',
    icon: 'cash-minus',
    color: Colors.pendingColor,
  },
  {
    id: 6,
    title: 'Payment to Admin',
    type: 'COD Settlement',
    amount: '₹ 900',
    from: 'Phlebo',
    to: 'Admin',
    direction: 'out',
    status: 'Paid',
    date: 'Aug 2, 2025 12:00 PM',
    icon: 'cash-remove',
    color: Colors.orange,
  },
  {
    id: 7,
    title: 'Admin Payout',
    type: 'Collection Charge',
    amount: '₹ 200',
    from: 'Admin',
    to: 'Phlebo',
    direction: 'in',
    status: 'Received',
    date: 'Aug 1, 2025 11:20 AM',
    icon: 'cash-plus',
    color: Colors.green,
  },
  {
    id: 8,
    title: 'COD Payment',
    type: 'COD Collection',
    amount: '₹ 1500',
    from: 'Patient',
    to: 'Phlebo',
    direction: 'in',
    status: 'Received',
    date: 'Jul 31, 2025 4:10 PM',
    icon: 'cash-minus',
    color: Colors.green,
  },
  {
    id: 9,
    title: 'Payment to Admin',
    type: 'COD Settlement',
    amount: '₹ 1500',
    from: 'Phlebo',
    to: 'Admin',
    direction: 'out',
    status: 'Paid',
    date: 'Jul 31, 2025 7:00 PM',
    icon: 'cash-remove',
    color: Colors.orange,
  },
  {
    id: 10,
    title: 'Admin Payout',
    type: 'Collection Charge',
    amount: '₹ 100',
    from: 'Admin',
    to: 'Phlebo',
    direction: 'in',
    status: 'Received',
    date: 'Jul 30, 2025 9:30 AM',
    icon: 'cash-plus',
    color: Colors.green,
  },
];



  const handleReturnCOD = () => {
    // Implement your backend/API logic here
    console.log('COD amount returned to Admin.');
    alert('COD amount returned to Admin.');
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header Summary */}
      <Text style={styles.headerTitle}>💼 Collection Overview</Text>

      {/* Collection Charge Card */}
      <View style={[styles.card, { borderLeftColor: Colors.primary }]}>
        <View style={styles.cardHeader}>
          <Icon name="bank-transfer-in" size={24} color={Colors.primary} />
          <Text style={styles.cardTitle}>Admin Collection</Text>
        </View>
        <Text style={styles.cardLabel}>Collection Charges Received</Text>
        <Text style={styles.cardAmount}>₹ 1500</Text>
      </View>

      {/* COD Collection Card */}
      <View style={[styles.card, { borderLeftColor: Colors.pendingColor }]}>
        <View style={styles.cardHeader}>
          <Icon name="account-cash" size={24} color={Colors.pendingColor} />
          <Text style={styles.cardTitle}>COD Collection</Text>
        </View>
        <Text style={styles.cardLabel}>Amount Collected from Patients</Text>
        <Text style={[styles.cardAmount, { color: Colors.pendingColor }]}>₹ 2000</Text>

        <TouchableOpacity style={styles.returnButton} onPress={handleReturnCOD}>
          <Icon name="cash-refund" size={20} color="white" />
          <Text style={styles.returnButtonText}>Return Amount to Admin</Text>
        </TouchableOpacity>
      </View>

      {/* Transaction History */}
      <Text style={[styles.headerTitle, { marginTop: responsiveHeight(3) }]}>
        📋 Transaction History
      </Text>
      <View style={styles.card}>
        {transactions.map((item) => (
          <View key={item.id} style={styles.transactionItem}>
            <Icon name={item.icon} size={24} color={item.color} style={styles.icon} />

            <View style={{ flex: 1 }}>
              <Text style={styles.transTitle}>
                {item.title} ({item.type})
              </Text>
              <Text style={styles.transDate}>{item.date}</Text>
            </View>

            <View style={{ alignItems: 'flex-end' }}>
              <Text style={styles.transAmount}>{item.amount}</Text>
              <Text style={[styles.transStatus, { color: item.color }]}>
                {item.status}
              </Text>
            </View>
          </View>
        ))}
      </View>
    </ScrollView>
  );
};

export default Transaction;

// Styles remain unchanged
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f1f4f9',
    padding: responsiveWidth(4),
  },
  headerTitle: {
    fontSize: responsiveFontSize(2.4),
    fontWeight: '700',
    color: Colors.primary,
    marginBottom: responsiveHeight(2),
  },
  card: {
    backgroundColor: Colors.white,
    borderRadius: 14,
    padding: responsiveWidth(4),
    marginBottom: responsiveHeight(2.5),
    elevation: 5,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    borderLeftWidth: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: responsiveHeight(1),
  },
  cardTitle: {
    fontSize: responsiveFontSize(2),
    fontWeight: '600',
    marginLeft: 10,
    color: Colors.black,
  },
  cardLabel: {
    fontSize: responsiveFontSize(1.8),
    color: '#555',
    marginBottom: 4,
  },
  cardAmount: {
    fontSize: responsiveFontSize(2.4),
    fontWeight: 'bold',
    color: Colors.green,
  },
  returnButton: {
    marginTop: responsiveHeight(2),
    backgroundColor: Colors.error,
    paddingVertical: responsiveHeight(1.2),
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  returnButtonText: {
    color: Colors.white,
    fontSize: responsiveFontSize(2),
    marginLeft: 8,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: responsiveHeight(1.6),
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  icon: {
    marginRight: 12,
  },
  transTitle: {
    fontSize: responsiveFontSize(1.9),
    fontWeight: '500',
    color: '#333',
  },
  transDate: {
    fontSize: responsiveFontSize(1.5),
    color: '#777',
  },
  transAmount: {
    fontSize: responsiveFontSize(1.9),
    fontWeight: 'bold',
  },
  transStatus: {
    fontSize: responsiveFontSize(1.5),
    marginTop: 2,
    fontWeight: '500',
  },
});
