import { Alert } from 'react-native';
import { launchImageLibrary } from "react-native-image-picker";

const DocumentPickers = async () => {
  const options = {
    mediaType: "photo", // For now, we'll use photo picker for documents
    quality: 0.8,
    maxWidth: 1000,
    maxHeight: 1000,
    selectionLimit: 1,
    includeBase64: false,
  };

  try {
    const result = await launchImageLibrary(options);

    if (result.didCancel) {
      console.log("User cancelled document picker");
      return null;
    } else if (result.errorCode) {
      console.log("DocumentPicker Error: ", result.errorMessage);
      Alert.alert('Error', 'Failed to pick document');
      return null;
    } else if (result.assets && result.assets.length > 0) {
      const allowedTypes = ["image/jpeg", "image/png", "image/webp"];
      const selectedDocument = result.assets.find(
        (doc) => allowedTypes.includes(doc.type)
      );
      
      if (selectedDocument) {
        return {
          uri: selectedDocument.uri,
          fileName: selectedDocument.fileName || 'document.jpg',
          type: selectedDocument.type,
          fileSize: selectedDocument.fileSize
        };
      } else {
        Alert.alert('Error', 'Please select a valid image file (JPG, PNG, WEBP)');
        return null;
      }
    }
  } catch (error) {
    console.log("Error picking document:", error);
    Alert.alert('Error', 'Failed to pick document');
    return null;
  }
}

export default DocumentPickers;
