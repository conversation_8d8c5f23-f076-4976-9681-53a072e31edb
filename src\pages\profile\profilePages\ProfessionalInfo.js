import { View, Text, StyleSheet, ScrollView, Alert, KeyboardAvoidingView, TouchableOpacity } from 'react-native'
import React, { useState } from 'react'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import InputBox from '../../../components/InputBox'
import Colors from '../../../styles/Colors'
import CustomDropdown from '../../../components/CustomDropdown'
import CustumBtn from '../../../components/CustumBtn'
import MyHeader from '../../../components/MyHeader'
import * as DocumentPicker from '@react-native-documents/picker';

const ProfessionalInfo = ({navigation}) => {
  // Professional Information
  const [qualification, setQualification] = useState('')
  const [certificationDocument, setCertificationDocument] = useState(null)
  const [totalExperience, setTotalExperience] = useState('')
  const [specialSkills, setSpecialSkills] = useState('')
  const [loading, setLoading] = useState(false)

  const qualificationData = [
    { label: 'DMLT (Diploma in Medical Laboratory Technology)', value: 'dmlt' },
    { label: 'BMLT (Bachelor in Medical Laboratory Technology)', value: 'bmlt' },
    { label: 'BSc Medical Laboratory Technology', value: 'bsc_mlt' },
    { label: 'MSc Medical Laboratory Technology', value: 'msc_mlt' },
    { label: 'Certificate in Phlebotomy', value: 'cert_phlebotomy' },
    { label: 'Other', value: 'other' },
  ];

  const experienceData = [
    { label: '0-1 years', value: '0-1' },
    { label: '1-2 years', value: '1-2' },
    { label: '2-5 years', value: '2-5' },
    { label: '5-10 years', value: '5-10' },
    { label: '10+ years', value: '10+' },
  ];


  const handleUploadDocument = async () => {
      try {
        const res = await DocumentPicker.pick({
          type: [DocumentPicker.types.images, DocumentPicker.types.pdf],
          allowMultiSelection: false,
        });
        if (res && res.length > 0) {
          const document = res[0];
          setCertificationDocument(document);
          console.log('Selected document:', document);
        }
      } catch (err) {
        if (DocumentPicker.isCancel(err)) {
          console.log('User cancelled upload');
        } else {
          console.error('Upload Error:', err);
        }
      }
    };


  const DocumentUploadBox = ({ label, document, onPress, icon = "file-document-outline" }) => (
    <TouchableOpacity onPress={onPress} style={styles.documentUploadBox}>
      <View style={styles.documentUploadContent}>
        <MaterialCommunityIcons name={icon} size={responsiveFontSize(2.5)} color={Colors.primary} />
        <View style={styles.documentUploadText}>
          <Text style={styles.documentUploadLabel}>{label}</Text>
          <Text style={styles.documentUploadStatus}>
            {document ? document.fileName || 'Document Selected' : 'Tap to upload'}
          </Text>
        </View>
        <MaterialCommunityIcons 
          name={document ? "check-circle" : "upload"} 
          size={responsiveFontSize(2)} 
          color={document ? Colors.green : Colors.gray} 
        />
      </View>
    </TouchableOpacity>
  );

  const handleSaveChanges = () => {
    setLoading(true)
    // Simulate API call
    setTimeout(() => {
      setLoading(false)
      Alert.alert('Success', 'Professional information updated successfully!')
    }, 2000)
  }

  return (
    <View style={styles.container}>
      <MyHeader title="Professional Information" onBackPress={() => navigation.goBack()} />
      <KeyboardAvoidingView behavior="padding" style={{ flex: 1 }}>
        <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={styles.contentContainer}>
          <View style={styles.content}>
            
            <CustomDropdown
              label="Qualification"
              data={qualificationData}
              value={qualification}
              onChange={setQualification}
              leftIcon="school-outline"
              placeholder="Select your qualification"
              search={true}
            />

            <DocumentUploadBox
              label="Certification (PDF/JPG)"
              document={certificationDocument}
              // onPress={handlePickDocument}
              onPress={handleUploadDocument}
              icon="certificate-outline"
            />

            <CustomDropdown
              label="Total Experience"
              data={experienceData}
              value={totalExperience}
              onChange={setTotalExperience}
              leftIcon="briefcase-outline"
              placeholder="Select experience range"
              search={false}
            />

            <InputBox
              label="Special Skills"
              value={specialSkills}
              onChangeText={setSpecialSkills}
              leftIcon="star-outline"
              placeholder="e.g., pediatric phlebotomy, geriatric care"
              multiline
            />
            
            <CustumBtn
              title="Save Changes"
              onPress={handleSaveChanges}
              loading={loading}
              disabled={loading}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>  
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  contentContainer: {
    flexGrow: 1,
  },
  content: {
    padding: responsiveWidth(5),
    gap: responsiveHeight(2)
  },
  documentUploadBox: {
    borderWidth: 1,
    borderColor: Colors.gray,
    borderRadius: 8,
    backgroundColor: Colors.surface,
    marginBottom: responsiveHeight(1),
  },
  documentUploadContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: responsiveWidth(4),
    gap: responsiveWidth(3),
  },
  documentUploadText: {
    flex: 1,
  },
  documentUploadLabel: {
    fontSize: responsiveFontSize(1.6),
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  documentUploadStatus: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.textSecondary,
  },
})

export default ProfessionalInfo
