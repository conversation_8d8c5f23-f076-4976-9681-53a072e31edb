import { View, Text, Image, Platform } from 'react-native'
import React from 'react'
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons'
import Colors from '../styles/Colors'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import Home from '../pages/home/<USER>'
import Task from '../pages/task/Task'
import Profile from '../pages/profile/Profile'

const Tab = createBottomTabNavigator()
const BottomTabNavigation = () => {
    const headerStyle = {
        headerShown: true,
        headerStyle: {
            backgroundColor: Colors.primary,
            borderBottomWidth: 0,
            elevation: 0,
        },
        headerTitleAlign: 'center',
        headerTintColor: Colors.white,
    }

    function LogoTitle({ title }) {
        return (
            <View style={{alignItems:'center',width:responsiveWidth(90)}}>
                {/* <Image
                    style={{ width: responsiveWidth(43), height: responsiveWidth(10), resizeMode: 'stretch',  }}
                    source={require('../accets/images/fullLogoRightText.png')} // your actual image path
                /> */}
                <Text style={{ fontSize: responsiveFontSize(2), fontWeight: 'bold', color: 'black' }}>
                    {title}
                </Text>
            </View>
        );
    }

    return (
        <Tab.Navigator
            screenOptions={({ route }) => ({

                tabBarIcon: ({ focused, color, size, }) => {

                    let iconName;
                    let IconComponent = MaterialCommunityIcons;

                    if (route.name === 'Home') {
                        iconName = focused ? 'home' : 'home-outline';
                    } else if (route.name === 'Task') {
                        iconName = focused ? 'medical-bag' : 'medical-bag';
                    } else if (route.name === 'LabBookings') {
                        iconName = focused ? 'clipboard-list' : 'clipboard-list-outline';
                    } else if (route.name === 'Profile') {
                        iconName = focused ? 'account' : 'account-outline';
                    }
                    return <IconComponent name={iconName} size={size} color={color} />;
                },
                tabBarActiveTintColor: Colors.primary,
                tabBarInactiveTintColor: Colors.tertiary,
                tabBarStyle: { backgroundColor: Colors.background },
                headerShown: false,
                // tabBarStyle: {
                //     backgroundColor: Colors.background,
                //     height: responsiveWidth(16),
                //     paddingBottom: Platform.OS === 'ios' ? responsiveWidth(2) : 0,
                // },
                // tabBarLabelStyle: {
                //     fontSize: responsiveFontSize(1.4),
                // },
                // headerStyle:{
                //    marginTop:0,
                //    backgroundColor:Colors.error,
                // }
            })}
        >
            <Tab.Screen name='Home' component={Home} options={{ title: 'Home' }} />
            <Tab.Screen name='Task' component={Task}
                options={{
                    headerBackVisible: true, headerShown: false,
                    headerTitle: () => (
                        <LogoTitle title="Today's Collection" />
                    ),
                    title:'Task',
                }} 
            />
            {/* <Tab.Screen name='LabBookings' component={LabBookings}  options={{
                    headerBackVisible: true, headerShown: true,
                    headerTitle: () => (
                        <LogoTitle title='Bookings & Reports' />
                    ),
                    title:'Booking',
                }} /> */}
            <Tab.Screen name='Profile' component={Profile} options={{ ...headerStyle, headerTitle: 'Profile', title: 'Profile', headerShown: false }} />
        </Tab.Navigator>
    )
}

export default BottomTabNavigation