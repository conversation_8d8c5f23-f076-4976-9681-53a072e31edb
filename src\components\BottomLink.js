import { View, Text, StyleSheet, TouchableOpacity } from 'react-native'
import React from 'react'
import { responsiveFontSize } from 'react-native-responsive-dimensions'
import Colors from '../styles/Colors'

const BottomLink = ({text, subText, subTextColor, navigation, onPress}) => {
  return (
    <View style={{flexDirection:'row'}}>
      {text && 
        <Text style={styles.text}>{text}</Text>
      }
        <TouchableOpacity onPress={onPress}>
            <Text style={[styles.text, { color: subTextColor,  }]}>{subText}</Text>
        </TouchableOpacity>
    </View>
  )
}

export default BottomLink

const styles = StyleSheet.create({
    text: {
        color: Colors.tertiary,
        fontSize: responsiveFontSize(1.7),
    },
})